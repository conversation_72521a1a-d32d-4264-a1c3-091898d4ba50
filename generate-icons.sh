#!/bin/bash

echo "🎨 Generating PWA icons for Wayne's Media Player..."

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Installing..."
    sudo apt-get update && sudo apt-get install -y imagemagick
fi

# Create icons directory
mkdir -p public/icons

# Icon sizes for PWA
sizes=(72 96 128 144 152 192 384 512)

# Create a simple music note icon using ImageMagick
for size in "${sizes[@]}"; do
    echo "Creating ${size}x${size} icon..."
    convert -size ${size}x${size} xc:"#14b8a6" \
        -fill white \
        -font DejaVu-Sans-Bold \
        -pointsize $((size/3)) \
        -gravity center \
        -annotate 0 "♪" \
        public/icons/icon-${size}x${size}.png
done

# Create shortcut icons
convert -size 96x96 xc:"#14b8a6" \
    -fill white \
    -font DejaVu-Sans-Bold \
    -pointsize 32 \
    -gravity center \
    -annotate 0 "▶" \
    public/icons/play-shortcut.png

convert -size 96x96 xc:"#14b8a6" \
    -fill white \
    -font DejaVu-Sans-Bold \
    -pointsize 32 \
    -gravity center \
    -annotate 0 "⚙" \
    public/icons/settings-shortcut.png

convert -size 96x96 xc:"#14b8a6" \
    -fill white \
    -font DejaVu-Sans-Bold \
    -pointsize 32 \
    -gravity center \
    -annotate 0 "✕" \
    public/icons/close-shortcut.png

echo "✅ Icons generated successfully!"
echo "📱 Your PWA now has proper icons for all devices."
