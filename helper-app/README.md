# <PERSON>'s Media Helper Application

This helper application scans your local and network drives for music files and makes them available to the Wayne's Media PWA.

## Running the Helper Application

You can run the helper application on a variety of platforms. Here are the instructions for each:

### Synology NAS (Recommended for NAS users)

Your Synology NAS supports Docker, which is the best way to run the helper application.

**1. Install Docker:**
   - Open the **Package Center** on your Synology NAS.
   - Search for "Docker" and install it.

**2. Enable SSH:**
   - Go to **Control Panel** > **Terminal & SNMP**.
   - Check the box to **Enable SSH service**.

**3. Set up the Helper Application on your NAS:**
   - Connect to your NAS via SSH.
   - Create a directory for the helper application:
     ```bash
     mkdir -p /volume1/docker/waynes-media-helper
     ```
   - Copy the contents of this `helper-app` directory to the new directory on your NAS.

**4. Build and Run the Docker Container:**
   - In your SSH session, navigate to the directory you created:
     ```bash
     cd /volume1/docker/waynes-media-helper
     ```
   - Build the Docker image:
     ```bash
     sudo docker build -t waynes-media-helper .
     ```
   - Run the Docker container:
     ```bash
     sudo docker run -d -p 3001:3001 --name waynes-media-helper --restart unless-stopped -v /path/to/your/music:/usr/src/app/music waynes-media-helper
     ```
     **Note:** Replace `/path/to/your/music` with the actual path to your music folder on the NAS.

### Windows, macOS, and Linux

**1. Install Node.js:**
   - Download and install Node.js from the official website: [https://nodejs.org](https://nodejs.org)

**2. Set up the Helper Application:**
   - Open a terminal (on macOS/Linux) or Command Prompt/PowerShell (on Windows).
   - Navigate to this `helper-app` directory.

**3. Install Dependencies:**
   - In the terminal, run the following command:
     ```bash
     npm install
     ```

**4. Run the Helper Application:**
   - In the terminal, run the following command:
     ```bash
     node index.js
     ```

---

Once the helper application is running, you can use the "Settings" page in the Wayne's Media PWA to add your music directories.