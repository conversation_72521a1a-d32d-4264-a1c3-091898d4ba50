{"name": "bonjour", "version": "3.5.0", "description": "A Bonjour/Zeroconf implementation in pure JavaScript", "main": "index.js", "dependencies": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}, "devDependencies": {"after-all": "^2.0.2", "standard": "^6.0.8", "tape": "^4.5.1"}, "scripts": {"test": "standard && tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/watson/bonjour.git"}, "keywords": ["bonjour", "zeroconf", "zero", "configuration", "mdns", "dns", "service", "discovery", "multicast", "broadcast", "dns-sd"], "author": "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)", "license": "MIT", "bugs": {"url": "https://github.com/watson/bonjour/issues"}, "homepage": "https://github.com/watson/bonjour", "coordinates": [55.68250900965318, 12.586377442991648]}