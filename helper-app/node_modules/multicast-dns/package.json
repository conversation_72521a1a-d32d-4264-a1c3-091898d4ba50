{"name": "multicast-dns", "version": "6.2.3", "description": "Low level multicast-dns implementation in pure javascript", "main": "index.js", "scripts": {"test": "standard && tape test.js"}, "bin": "cli.js", "dependencies": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}, "devDependencies": {"standard": "^10.0.3", "tape": "^4.8.0"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/multicast-dns.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/multicast-dns/issues"}, "homepage": "https://github.com/mafintosh/multicast-dns", "keywords": ["multicast", "dns", "mdns", "multicastdns", "dns-sd", "service", "discovery", "bonjour", "avahi"], "coordinates": [55.6465878, 12.5492251]}