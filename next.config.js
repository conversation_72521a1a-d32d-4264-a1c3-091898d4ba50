/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Exclude react-native-fs from the server-side build
    if (isServer) {
      config.externals.push('react-native-fs');
    }

    // Add a fallback for the 'fs' module in the browser
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };

    return config;
  },
};

module.exports = nextConfig;