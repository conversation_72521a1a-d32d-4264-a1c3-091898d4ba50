{"name": "Wayne's Media Player", "short_name": "Wayne's Media", "description": "Cross-platform PWA for streaming local and network media", "start_url": "/", "display": "standalone", "background_color": "#000000", "theme_color": "#14b8a6", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["music", "entertainment", "multimedia"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/screenshots/desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide"}, {"src": "/screenshots/mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow"}], "shortcuts": [{"name": "Play Music", "short_name": "Play", "description": "Start playing music", "url": "/?action=play", "icons": [{"src": "/icons/play-shortcut.png", "sizes": "96x96"}]}, {"name": "Settings", "short_name": "Settings", "description": "Open app settings", "url": "/?view=settings", "icons": [{"src": "/icons/settings-shortcut.png", "sizes": "96x96"}]}], "share_target": {"action": "/share", "method": "POST", "enctype": "multipart/form-data", "params": {"files": [{"name": "audio", "accept": ["audio/*"]}]}}, "file_handlers": [{"action": "/", "accept": {"audio/*": [".mp3", ".wav", ".ogg", ".flac", ".m4a", ".aac"]}}], "protocol_handlers": [{"protocol": "web+music", "url": "/?url=%s"}]}