"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import * as mm from 'music-metadata-browser';
import { MediaPlayer } from '@/components/MediaPlayer';
import { Playlist } from '@/components/Playlist';

export default function Home() {
  const [musicFiles, setMusicFiles] = useState<any[]>([]);
  const [playlist, setPlaylist] = useState<any[]>([]);
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentView, setCurrentView] = useState('collection'); // collection, network, isos
  const [musicDirs, setMusicDirs] = useState<any[]>([]);
  const [newDir, setNewDir] = useState('');
  const [helperAppUrl, setHelperAppUrl] = useState('http://localhost:3001'); // Default URL for the helper app
  const [networkSources, setNetworkSources] = useState<any[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('online');
  const [showPlaylist, setShowPlaylist] = useState(false);

  const fileInputRef = useRef<any>(null);

  // Detect platform and capabilities
  const detectPlatform = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();

    return {
      isMobile: /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent),
      isIOS: /ipad|iphone|ipod/.test(userAgent),
      isAndroid: /android/.test(userAgent),
      isDesktop: !(/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)),
      isMac: platform.includes('mac'),
      isWindows: platform.includes('win'),
      isLinux: platform.includes('linux'),
      supportsFileAPI: 'File' in window && 'FileReader' in window && 'FileList' in window && 'Blob' in window,
      supportsMediaSession: 'mediaSession' in navigator,
      supportsServiceWorker: 'serviceWorker' in navigator,
      supportsNotifications: 'Notification' in window
    };
  };

  // Enhanced network discovery for cross-platform media sources
  const scanNetworkSources = async () => {
    setIsScanning(true);
    try {
      console.log('Starting network discovery...');

      // Discover network devices
      const networkResponse = await fetch(`${helperAppUrl}/api/discover-network`);
      if (networkResponse.ok) {
        const networkDevices = await networkResponse.json();
        console.log('Network devices found:', networkDevices);

        // Discover local directories
        const localResponse = await fetch(`${helperAppUrl}/api/discover-local`);
        const localDirs = localResponse.ok ? await localResponse.json() : [];
        console.log('Local directories found:', localDirs);

        // Combine network and local sources
        const allSources = [
          ...networkDevices.map((device: any) => ({
            ...device,
            category: 'network',
            status: 'discovered'
          })),
          ...localDirs.map((dir: any) => ({
            name: `Local: ${dir.path}`,
            type: 'local',
            protocol: 'file',
            host: 'localhost',
            url: dir.path,
            category: 'local',
            status: 'available',
            platform: dir.platform,
            discovered: new Date().toISOString()
          }))
        ];

        setNetworkSources(allSources);
        console.log('Total sources discovered:', allSources.length);
      }
    } catch (error) {
      console.error('Network scan failed:', error);

      // Enhanced fallback with platform detection
      try {
        const platformResponse = await fetch(`${helperAppUrl}/api/platform`);
        const platformInfo = platformResponse.ok ? await platformResponse.json() : null;

        const fallbackSources = [
          { name: 'Local Network Music', url: 'http://*************:8080/music', type: 'http', category: 'network' },
          { name: 'NAS Music Share', url: 'smb://nas.local/music', type: 'smb', category: 'network' },
          { name: 'Media Server', url: 'http://mediaserver.local:8200', type: 'dlna', category: 'network' },
          { name: 'Plex Server', url: 'http://*************:32400', type: 'plex', category: 'network' },
          { name: 'Jellyfin Server', url: 'http://*************:8096', type: 'jellyfin', category: 'network' }
        ];

        if (platformInfo) {
          // Add platform-specific local directories
          platformInfo.defaultMusicDirs.forEach((dir: any) => {
            fallbackSources.push({
              name: `Local: ${dir}`,
              url: dir,
              type: 'local',
              category: 'local'
            });
          });
        }

        setNetworkSources(fallbackSources);
      } catch (fallbackError) {
        console.error('Fallback discovery failed:', fallbackError);
        setNetworkSources([]);
      }
    }
    setIsScanning(false);
  };

  // Auto-discover sources on component mount
  const autoDiscoverSources = async () => {
    try {
      const statusResponse = await fetch(`${helperAppUrl}/api/discovery-status`);
      if (statusResponse.ok) {
        const status = await statusResponse.json();
        if (!status.inProgress && status.devicesFound === 0) {
          // Only auto-discover if no previous discovery was done
          await scanNetworkSources();
        }
      }
    } catch (error) {
      console.log('Auto-discovery skipped:', error);
    }
  };

  // Check connection status
  const checkConnectionStatus = () => {
    setConnectionStatus(navigator.onLine ? 'online' : 'offline');
  };

  // Test connection to a network source
  const testConnection = async (source: any) => {
    try {
      const response = await fetch(`${helperAppUrl}/api/test-connection`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          host: source.host,
          port: source.port,
          protocol: source.protocol
        })
      });

      const result = await response.json();
      console.log('Connection test result:', result);

      // Update the source status in the UI
      setNetworkSources(prev => prev.map(s =>
        s.url === source.url ? { ...s, status: result.success ? 'connected' : 'failed' } : s
      ));

      return result.success;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  };

  // Add a custom network source
  const addNetworkSource = async (url: any, name: any) => {
    if (!url.trim()) return;

    try {
      // Parse the URL to extract components
      const urlObj = new URL(url);
      const newSource = {
        name: name || `Custom: ${urlObj.hostname}`,
        url: url,
        type: urlObj.protocol.replace(':', ''),
        protocol: urlObj.protocol.replace(':', ''),
        host: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        category: 'network',
        status: 'added',
        discovered: new Date().toISOString()
      };

      setNetworkSources(prev => [...prev, newSource]);

      // Test the connection
      await testConnection(newSource);

    } catch (error) {
      console.error('Invalid URL:', error);
      // Add as a simple entry anyway
      const newSource = {
        name: name || `Custom: ${url}`,
        url: url,
        type: 'custom',
        protocol: 'unknown',
        category: 'network',
        status: 'added',
        discovered: new Date().toISOString()
      };

      setNetworkSources(prev => [...prev, newSource]);
    }
  };

  const fetchConfig = useCallback(async () => {
    try {
      const response = await fetch(`${helperAppUrl}/api/config`);
      const data = await response.json();
      setMusicDirs(data.musicDirs);
    } catch (error) {
      console.error('Error fetching config:', error);
    }
  }, [helperAppUrl]);

  const saveConfig = useCallback(async (newDirs: any) => {
    try {
      await fetch(`${helperAppUrl}/api/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ musicDirs: newDirs }),
      });
      setMusicDirs(newDirs);
    } catch (error) {
      console.error('Error saving config:', error);
    }
  }, [helperAppUrl]);

  const handleAddDir = () => {
    console.log('handleAddDir called');
    console.log('newDir:', newDir);
    console.log('musicDirs:', musicDirs);
    if (newDir && !musicDirs.includes(newDir)) {
      console.log('Adding new directory:', newDir);
      const newDirs = [...musicDirs, newDir];
      saveConfig(newDirs);
      setNewDir('');
    } else {
      console.log('Condition not met: newDir is empty or already exists.');
    }
  };

  const handleRemoveDir = (dirToRemove: any) => {
    const newDirs = musicDirs.filter(dir => dir !== dirToRemove);
    saveConfig(newDirs);
  };

  const fetchMusicFiles = useCallback(async () => {
    try {
      const response = await fetch(`${helperAppUrl}/api/music`);
      const data = await response.json();
      const newMusicFiles = await Promise.all(data.map(async (file: any) => {
        const fileUrl = `${helperAppUrl}/api/music-file?path=${encodeURIComponent(file.path)}`;
        // We can't get metadata from a remote file directly in the browser
        // The helper app would need to be updated to extract metadata
        return {
          ...file,
          url: fileUrl,
        };
      }));
      setMusicFiles(newMusicFiles);
    } catch (error) {
      console.error('Error fetching music files:', error);
    }
  }, [helperAppUrl]);

  // Playlist management functions
  const addToPlaylist = (track: any) => {
    setPlaylist(prev => [...prev, track]);
  };

  const removeFromPlaylist = (index: any) => {
    setPlaylist(prev => {
      const newPlaylist = prev.filter((_, i) => i !== index);
      if (index === currentTrackIndex && newPlaylist.length > 0) {
        setCurrentTrackIndex(Math.min(currentTrackIndex, newPlaylist.length - 1));
      } else if (index < currentTrackIndex) {
        setCurrentTrackIndex(prev => prev - 1);
      }
      return newPlaylist;
    });
  };

  const clearPlaylist = () => {
    setPlaylist([]);
    setCurrentTrackIndex(0);
  };

  const playTrack = (track: any) => {
    // Add to playlist if not already there
    const existingIndex = playlist.findIndex(t => t.url === track.url);
    if (existingIndex >= 0) {
      setCurrentTrackIndex(existingIndex);
    } else {
      setPlaylist(prev => [...prev, track]);
      setCurrentTrackIndex(playlist.length);
    }
  };

  const playAll = (tracks: any) => {
    setPlaylist(tracks);
    setCurrentTrackIndex(0);
  };

  useEffect(() => {
    fetchConfig();
    fetchMusicFiles();
    checkConnectionStatus();
    autoDiscoverSources(); // Auto-discover media sources

    // Listen for online/offline events
    window.addEventListener('online', checkConnectionStatus);
    window.addEventListener('offline', checkConnectionStatus);

    return () => {
      window.removeEventListener('online', checkConnectionStatus);
      window.removeEventListener('offline', checkConnectionStatus);
    };
  }, [fetchConfig, fetchMusicFiles]);




  const handleFileChange = async (event: any) => {
    const files = Array.from(event.target.files);

    const newMusicFiles = files.map((file: any) => {
      const fileUrl = URL.createObjectURL(file);

      return {
        file,
        name: file.name,
        artist: 'Unknown Artist',
        album: 'Unknown Album',
        picture: null,
        url: fileUrl,
        type: 'local'
      };
    });

    setMusicFiles(prevFiles => [...prevFiles, ...newMusicFiles]);
  };

  const handleAddMusicClick = () => {
    fileInputRef.current?.click();
  };

  // Initialize playlist with all music files when they're loaded
  useEffect(() => {
    if (musicFiles.length > 0 && playlist.length === 0) {
      setPlaylist(musicFiles);
    }
  }, [musicFiles, playlist.length]);

  const renderView = () => {
    switch (currentView) {
      case 'collection':
        return (
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4">
            {musicFiles.map((song, index) => (
              <div
                key={index}
                className="bg-gray-900 rounded-lg overflow-hidden shadow-lg cursor-pointer touch-manipulation hover:bg-gray-800 transition-colors"
                onClick={() => playTrack(song)}
              >
                <div className="w-full h-32 md:h-32 bg-gray-700">
                  {song.picture && <img src={song.picture} alt={song.name} className="w-full h-full object-cover" />}
                </div>
                <div className="p-3 md:p-2">
                  <h3 className="font-normal text-sm md:text-base truncate">{song.name}</h3>
                  <p className="text-xs md:text-sm text-gray-400 truncate">{song.artist}</p>
                </div>
              </div>
            ))}
          </div>
        );
      case 'network':
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-4">Network Discovery</h2>
              <p className="text-gray-400">This feature is under development.</p>
              <p className="text-gray-400">It will allow you to discover and play music from other devices on your local network.</p>
            </div>
          </div>
        );
      case 'isos':
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-4">ISO Support</h2>
              <p className="text-gray-400">This feature is under development.</p>
              <p className="text-gray-400">It will allow you to browse and play music from ISO files.</p>
            </div>
          </div>
        );
      case 'network':
        return (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-semibold">Network Sources</h2>
              <button
                onClick={scanNetworkSources}
                disabled={isScanning}
                className="bg-teal-500 text-white font-semibold py-2 px-4 rounded hover:bg-teal-600 disabled:opacity-50"
              >
                {isScanning ? 'Scanning...' : 'Scan Network'}
              </button>
            </div>

            <div className="mb-6">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                connectionStatus === 'online' ? 'bg-green-600' : 'bg-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  connectionStatus === 'online' ? 'bg-green-300' : 'bg-red-300'
                }`}></div>
                {connectionStatus === 'online' ? 'Online' : 'Offline'}
              </div>
            </div>

            <div className="space-y-6">
              {networkSources.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  <p>No media sources found.</p>
                  <p className="text-sm mt-2">Click "Scan Network" to discover local and network media sources.</p>
                </div>
              ) : (
                <>
                  {/* Local Sources */}
                  {networkSources.filter(s => s.category === 'local').length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-teal-400">📁 Local Sources</h3>
                      <div className="grid gap-3">
                        {networkSources.filter(s => s.category === 'local').map((source, index) => (
                          <div key={`local-${index}`} className="bg-gray-700 p-4 rounded-lg">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h4 className="font-semibold">{source.name}</h4>
                                <p className="text-gray-400 text-sm">{source.url}</p>
                                <div className="flex items-center gap-2 mt-2">
                                  <span className="inline-block px-2 py-1 rounded text-xs bg-green-600">
                                    LOCAL
                                  </span>
                                  {source.platform && (
                                    <span className="inline-block px-2 py-1 rounded text-xs bg-gray-600">
                                      {source.platform.toUpperCase()}
                                    </span>
                                  )}
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  // Scan local directory
                                  fetch(`${helperAppUrl}/api/scan-directory`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ directory: source.url, maxDepth: 3 })
                                  })
                                  .then(res => res.json())
                                  .then(data => {
                                    console.log('Directory scan result:', data);
                                    alert(`Found ${data.fileCount} audio files in ${source.url}`);
                                  })
                                  .catch(err => {
                                    console.error('Scan failed:', err);
                                    alert('Failed to scan directory');
                                  });
                                }}
                                className="bg-teal-500 text-white px-3 py-1 rounded text-sm hover:bg-teal-600"
                              >
                                Scan
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Network Sources */}
                  {networkSources.filter(s => s.category === 'network').length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-blue-400">🌐 Network Sources</h3>
                      <div className="grid gap-3">
                        {networkSources.filter(s => s.category === 'network').map((source, index) => (
                          <div key={`network-${index}`} className="bg-gray-700 p-4 rounded-lg">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h4 className="font-semibold">{source.name}</h4>
                                <p className="text-gray-400 text-sm">{source.url}</p>
                                <div className="flex items-center gap-2 mt-2">
                                  <span className={`inline-block px-2 py-1 rounded text-xs ${
                                    source.type === 'dlna' || source.type === 'upnp' ? 'bg-blue-600' :
                                    source.type === 'http' ? 'bg-green-600' :
                                    source.type === 'smb' ? 'bg-purple-600' :
                                    source.type === 'bonjour' ? 'bg-orange-600' :
                                    source.type === 'plex' ? 'bg-yellow-600' :
                                    source.type === 'jellyfin' ? 'bg-indigo-600' : 'bg-gray-600'
                                  }`}>
                                    {source.protocol ? source.protocol.toUpperCase() : source.type.toUpperCase()}
                                  </span>
                                  {source.host && (
                                    <span className="text-xs text-gray-400">{source.host}:{source.port}</span>
                                  )}
                                  {source.discovered && (
                                    <span className="text-xs text-green-400">✓ Discovered</span>
                                  )}
                                </div>
                              </div>
                              <button
                                onClick={() => testConnection(source)}
                                className={`px-3 py-1 rounded text-sm ${
                                  source.status === 'connected' ? 'bg-green-600 hover:bg-green-700' :
                                  source.status === 'failed' ? 'bg-red-600 hover:bg-red-700' :
                                  'bg-teal-500 hover:bg-teal-600'
                                } text-white`}
                              >
                                {source.status === 'connected' ? 'Connected' :
                                 source.status === 'failed' ? 'Failed' : 'Connect'}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Add Network Source</h3>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Enter network URL (http://, smb://, ftp://, etc.)"
                    className="flex-1 p-2 bg-gray-700 rounded"
                    value={newDir}
                    onChange={(e) => setNewDir(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addNetworkSource(newDir, newDir);
                        setNewDir('');
                      }
                    }}
                  />
                  <button
                    onClick={() => {
                      addNetworkSource(newDir, newDir);
                      setNewDir('');
                    }}
                    className="bg-teal-500 text-white font-semibold py-2 px-4 rounded hover:bg-teal-600"
                  >
                    Add Source
                  </button>
                </div>
                <div className="text-sm text-gray-400">
                  <p>Examples:</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>http://*************:8080/music</li>
                    <li>smb://nas.local/music</li>
                    <li>ftp://mediaserver.local/audio</li>
                    <li>http://plex.local:32400</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-black text-white font-light">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={`bg-gray-900 p-6 flex-col justify-between ${sidebarOpen ? 'flex fixed inset-y-0 left-0 z-50 w-64' : 'hidden'} md:flex md:relative md:w-64 md:z-auto`}>
        <div>
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl font-semibold">Wayne's Media</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="md:hidden text-gray-400 hover:text-white p-2 touch-manipulation"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <nav>
            <ul>
              <li className="mb-4">
                <a href="#" onClick={() => setCurrentView('collection')} className={`flex items-center text-lg hover:text-white ${currentView === 'collection' ? 'text-white' : 'text-gray-300'}`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2H5a2 2 0 00-2 2v2m14 0h-2M5 11H3" /></svg>
                  My Collection
                </a>
              </li>
              <li className="mb-4">
                <a href="#" onClick={() => setCurrentView('network')} className={`flex items-center text-lg hover:text-white ${currentView === 'network' ? 'text-white' : 'text-gray-300'}`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" /></svg>
                  Network
                </a>
              </li>
              <li className="mb-4">
                <a href="#" onClick={() => setCurrentView('isos')} className={`flex items-center text-lg hover:text-white ${currentView === 'isos' ? 'text-white' : 'text-gray-300'}`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                  ISOs
                </a>
              </li>
              <li>
                <a href="/settings" className="flex items-center text-lg hover:text-white text-gray-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                  Settings
                </a>
              </li>
            </ul>
          </nav>
        </div>
        <div>
          <input
            type="file"
            multiple
            accept="audio/*,.iso"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
          />
          <button
            onClick={handleAddMusicClick}
            className="w-full bg-teal-500 text-white font-semibold py-2 px-4 rounded-lg hover:bg-teal-600"
          >
            Add Music or ISO
          </button>
        </div>
      </aside>

      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-gray-800 p-4 flex items-center justify-between md:hidden">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-gray-300 hover:text-white p-2 touch-manipulation"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 className="text-xl font-semibold">Wayne's Media</h1>
          <div className="w-10"></div> {/* Spacer for centering */}
        </header>

        {/* Main Content */}
        <div className="flex flex-1 overflow-hidden">
          <main className="flex-1 p-4 md:p-8 overflow-y-auto bg-gray-800">
            {renderView()}
          </main>

          {/* Playlist Sidebar */}
          {showPlaylist && (
            <aside className="w-80 bg-gray-900 border-l border-gray-800 overflow-hidden">
              <Playlist
                tracks={playlist}
                currentTrackIndex={currentTrackIndex}
                onTrackSelect={setCurrentTrackIndex}
                onTrackRemove={removeFromPlaylist}
                onPlaylistClear={clearPlaylist}
                className="h-full"
              />
            </aside>
          )}
        </div>

        {/* Media Player */}
        <MediaPlayer
          playlist={playlist}
          currentTrackIndex={currentTrackIndex}
          onTrackChange={setCurrentTrackIndex}
          onPlaylistEnd={() => console.log('Playlist ended')}
        />
      </div>
    </div>
  );
}