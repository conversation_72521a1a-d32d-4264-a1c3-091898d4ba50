'use client';

import React from 'react';
import { useSettings } from './SettingsContext';
import { useTheme } from '@/components/ThemeProvider';

export function AppearanceSettings() {
  const { updateAppearanceSettings } = useSettings();
  const { theme, setTheme } = useTheme();

  const themes = [
    { value: 'dark', label: 'Dark', description: 'Dark theme optimized for low-light environments' },
    { value: 'light', label: 'Light', description: 'Light theme for bright environments' },
    { value: 'auto', label: 'Auto', description: 'Automatically switch based on system preference' },
  ];

  const handleThemeChange = async (newTheme: 'dark' | 'light' | 'auto') => {
    // Update both the theme provider and settings context
    setTheme(newTheme);
    await updateAppearanceSettings({ theme: newTheme });
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Appearance</h2>
        <p className="text-gray-400 text-sm">
          Customize the look and feel of your media player.
        </p>
      </div>

      <div className="space-y-6">
        {/* Theme Selection */}
        <div>
          <h3 className="text-sm font-medium text-white mb-3">Theme</h3>
          <div className="space-y-2">
            {themes.map((themeOption) => (
              <label
                key={themeOption.value}
                className="flex items-center p-3 bg-gray-800 rounded-md hover:bg-gray-750 cursor-pointer transition-colors"
              >
                <input
                  type="radio"
                  name="theme"
                  value={themeOption.value}
                  checked={theme === themeOption.value}
                  onChange={() => handleThemeChange(themeOption.value as 'dark' | 'light' | 'auto')}
                  className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-600 bg-gray-700"
                />
                <div className="ml-3">
                  <div className="text-sm font-medium text-white">{themeOption.label}</div>
                  <div className="text-sm text-gray-400">{themeOption.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Additional appearance settings placeholder */}
        <div>
          <h3 className="text-sm font-medium text-white mb-3">Additional Options</h3>
          <div className="bg-gray-800 rounded-md p-4">
            <p className="text-sm text-gray-400 text-center">
              More appearance customization options will be added here
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
