'use client';

import React, { useState, useEffect } from 'react';
import { useSettings } from './SettingsContext';
import { SettingsCard } from './SettingsCard';
import { SettingsSection } from './SettingsSection';
import { SettingsRow } from './SettingsRow';
import { Button } from '../ui/Button';
import { Input } from '@/components/ui/Input';

export function HelperAppSettings() {
  const { state, updateHelperAppUrl, refreshConfig } = useSettings();
  const [url, setUrl] = useState(state.helperAppUrl);
  const [isValid, setIsValid] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');
  const [isRetesting, setIsRetesting] = useState(false);

  // Validate URL format
  const validateUrl = (urlString: string) => {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  };

  // Test connection to helper app
  const testConnection = async (testUrl: string) => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`${testUrl}/api/config`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.log('Connection test failed:', error);
      return false;
    }
  };

  // Handle URL change
  const handleUrlChange = (newUrl: string) => {
    setUrl(newUrl);
    const valid = validateUrl(newUrl);
    setIsValid(valid);
    
    if (valid) {
      updateHelperAppUrl(newUrl);
    }
  };

  // Test connection when URL changes
  useEffect(() => {
    if (!isValid) {
      setConnectionStatus('unknown');
      return;
    }

    const checkConnection = async () => {
      setConnectionStatus('unknown');
      const connected = await testConnection(url);
      setConnectionStatus(connected ? 'connected' : 'disconnected');
    };

    const timeoutId = setTimeout(checkConnection, 1000); // Debounce
    return () => clearTimeout(timeoutId);
  }, [url, isValid]);

  // Sync with context state
  useEffect(() => {
    setUrl(state.helperAppUrl);
  }, [state.helperAppUrl]);

  // Handle manual re-scan
  const handleReScan = async () => {
    setIsRetesting(true);
    try {
      const connected = await testConnection(url);
      setConnectionStatus(connected ? 'connected' : 'disconnected');
      if (connected) {
        await refreshConfig();
      }
    } finally {
      setIsRetesting(false);
    }
  };

  return (
    <SettingsCard
      title="Helper App Connection"
      description="Configure the URL for the helper application that manages your media files"
      icon={
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
      }
    >

      <div className="space-y-6">
        <SettingsSection title="Connection Settings">
          <SettingsRow
            label="Helper App URL"
            description="The helper app should be running on this URL to manage your media files"
            action={
              <div className="flex-1 max-w-md relative">
                <Input
                  id="helper-url"
                  type="url"
                  value={url}
                  onChange={(e) => handleUrlChange(e.target.value)}
                  placeholder="http://localhost:3001"
                  error={!isValid ? 'Please enter a valid URL (e.g., http://localhost:3001)' : undefined}
                  fullWidth
                  rightIcon={
                    <div className="flex items-center">
                      {connectionStatus === 'connected' && (
                        <div className="h-2 w-2 bg-green-400 rounded-full" title="Connected"></div>
                      )}
                      {connectionStatus === 'disconnected' && (
                        <div className="h-2 w-2 bg-red-400 rounded-full" title="Disconnected"></div>
                      )}
                      {connectionStatus === 'unknown' && (
                        <div className="h-2 w-2 bg-gray-400 rounded-full" title="Unknown"></div>
                      )}
                    </div>
                  }
                />
              </div>
            }
          />
        </SettingsSection>

        {/* Connection Status */}
        <SettingsSection title="Connection Status">
          <div className="flex items-center justify-between p-4 bg-gray-800 rounded-md">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                {connectionStatus === 'connected' && (
                  <svg className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
                {connectionStatus === 'disconnected' && (
                  <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
                {connectionStatus === 'unknown' && (
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-white">
                  {connectionStatus === 'connected' && 'Connected to helper app'}
                  {connectionStatus === 'disconnected' && 'Cannot connect to helper app'}
                  {connectionStatus === 'unknown' && 'Checking connection...'}
                </p>
                <p className="text-xs text-gray-400">
                  {connectionStatus === 'connected' && 'All features are available'}
                  {connectionStatus === 'disconnected' && 'Make sure the helper app is running'}
                  {connectionStatus === 'unknown' && 'Testing connection to helper app'}
                </p>
              </div>
            </div>

            {/* Re-Scan Button - Always available */}
            <Button
              variant={connectionStatus === 'connected' ? 'ghost' : 'secondary'}
              onClick={handleReScan}
              disabled={isRetesting}
              className="flex items-center space-x-2 w-full md:w-auto"
              size="md"
            >
              {isRetesting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  <span>Scanning...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>{connectionStatus === 'connected' ? 'Refresh Connection' : 'Re-Scan'}</span>
                </>
              )}
            </Button>
          </div>
        </SettingsSection>

        {/* Helper App Management Section */}
        <SettingsSection title="Helper App Management">
          <SettingsRow
            label="Refresh Connection"
            description="Test the connection to the helper app and refresh the configuration if needed"
            action={
              <Button
                variant="primary"
                onClick={handleReScan}
                disabled={isRetesting}
                className="flex items-center space-x-2"
                size="md"
              >
                {isRetesting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    <span>Scanning...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span>Refresh</span>
                  </>
                )}
              </Button>
            }
          />
        </SettingsSection>
      </div>
    </SettingsCard>
  );
}
