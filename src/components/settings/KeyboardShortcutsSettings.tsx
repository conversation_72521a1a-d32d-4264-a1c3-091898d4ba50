'use client';

import React, { useState } from 'react';
import { useSettings, KeyboardShortcuts } from './SettingsContext';
import { SettingsCard } from './SettingsCard';
import { SettingsSection } from './SettingsSection';
import { SettingsRow } from './SettingsRow';
import { Button } from '../ui/Button';

interface ShortcutDefinition {
  key: keyof KeyboardShortcuts;
  label: string;
  description: string;
  category: 'playback' | 'navigation' | 'volume' | 'general';
}

const shortcutDefinitions: ShortcutDefinition[] = [
  // Playback controls
  { key: 'playPause', label: 'Play/Pause', description: 'Toggle playback', category: 'playback' },
  { key: 'nextTrack', label: 'Next Track', description: 'Skip to next track', category: 'playback' },
  { key: 'previousTrack', label: 'Previous Track', description: 'Go to previous track', category: 'playback' },
  { key: 'shuffle', label: 'Shuffle', description: 'Toggle shuffle mode', category: 'playback' },
  { key: 'repeat', label: 'Repeat', description: 'Toggle repeat mode', category: 'playback' },
  
  // Navigation
  { key: 'seekForward', label: 'Seek Forward', description: 'Jump forward 10 seconds', category: 'navigation' },
  { key: 'seekBackward', label: 'Seek Backward', description: 'Jump backward 10 seconds', category: 'navigation' },
  
  // Volume controls
  { key: 'volumeUp', label: 'Volume Up', description: 'Increase volume', category: 'volume' },
  { key: 'volumeDown', label: 'Volume Down', description: 'Decrease volume', category: 'volume' },
  { key: 'mute', label: 'Mute', description: 'Toggle mute', category: 'volume' },
  
  // General
  { key: 'openSettings', label: 'Open Settings', description: 'Open settings page', category: 'general' },
  { key: 'toggleFullscreen', label: 'Fullscreen', description: 'Toggle fullscreen mode', category: 'general' },
];

const categoryLabels = {
  playback: 'Playback Controls',
  navigation: 'Navigation',
  volume: 'Volume Controls',
  general: 'General',
};

export function KeyboardShortcutsSettings() {
  const { state, updateKeyboardShortcuts } = useSettings();
  const { keyboardShortcuts } = state;
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [recordingKey, setRecordingKey] = useState<string | null>(null);
  const [tempShortcut, setTempShortcut] = useState('');

  const formatKeyDisplay = (key: string): string => {
    return key
      .replace('Key', '')
      .replace('Arrow', '')
      .replace('Shift+', '⇧ ')
      .replace('Ctrl+', '⌃ ')
      .replace('Alt+', '⌥ ')
      .replace('Meta+', '⌘ ')
      .replace('Space', '␣');
  };

  const handleKeyRecord = (shortcutKey: string) => {
    setEditingKey(shortcutKey);
    setRecordingKey(shortcutKey);
    setTempShortcut('');
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!recordingKey) return;
    
    event.preventDefault();
    event.stopPropagation();

    const parts: string[] = [];
    
    if (event.shiftKey) parts.push('Shift');
    if (event.ctrlKey) parts.push('Ctrl');
    if (event.altKey) parts.push('Alt');
    if (event.metaKey) parts.push('Meta');
    
    let keyName = event.code;
    
    // Handle special keys
    if (event.code === 'Space') {
      keyName = 'Space';
    } else if (event.code.startsWith('Arrow')) {
      keyName = event.code;
    } else if (event.code.startsWith('Key')) {
      keyName = event.code;
    } else if (event.code.startsWith('Digit')) {
      keyName = event.code;
    } else {
      keyName = event.code;
    }
    
    if (keyName !== 'Shift' && keyName !== 'Control' && keyName !== 'Alt' && keyName !== 'Meta') {
      parts.push(keyName);
      const newShortcut = parts.join('+');
      setTempShortcut(newShortcut);
    }
  };

  const handleSaveShortcut = async () => {
    if (!recordingKey || !tempShortcut) return;
    
    await updateKeyboardShortcuts({ [recordingKey]: tempShortcut });
    setEditingKey(null);
    setRecordingKey(null);
    setTempShortcut('');
  };

  const handleCancelEdit = () => {
    setEditingKey(null);
    setRecordingKey(null);
    setTempShortcut('');
  };

  const handleResetToDefaults = async () => {
    const defaultShortcuts = {
      playPause: 'Space',
      nextTrack: 'ArrowRight',
      previousTrack: 'ArrowLeft',
      volumeUp: 'ArrowUp',
      volumeDown: 'ArrowDown',
      mute: 'KeyM',
      shuffle: 'KeyS',
      repeat: 'KeyR',
      seekForward: 'Shift+ArrowRight',
      seekBackward: 'Shift+ArrowLeft',
      openSettings: 'Comma',
      toggleFullscreen: 'KeyF',
    };
    
    await updateKeyboardShortcuts(defaultShortcuts);
  };

  const groupedShortcuts = shortcutDefinitions.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, ShortcutDefinition[]>);

  return (
    <SettingsCard
      title="Keyboard Shortcuts"
      description="Customize keyboard shortcuts for media controls and navigation"
      icon={
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      }
    >
      <div className="space-y-6">
        {/* Reset to defaults */}
        <div className="flex justify-end">
          <Button
            variant="secondary"
            onClick={handleResetToDefaults}
            className="text-sm"
          >
            Reset to Defaults
          </Button>
        </div>

        {/* Keyboard recording area */}
        {recordingKey && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onKeyDown={handleKeyDown}
            tabIndex={-1}
          >
            <div className="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-white mb-4">
                Recording shortcut for: {shortcutDefinitions.find(s => s.key === recordingKey)?.label}
              </h3>
              <p className="text-gray-300 mb-4">
                Press the key combination you want to use:
              </p>
              <div className="bg-gray-900 border border-gray-600 rounded-lg p-4 mb-4 min-h-[60px] flex items-center justify-center">
                <span className="text-xl font-mono text-teal-400">
                  {tempShortcut ? formatKeyDisplay(tempShortcut) : 'Press keys...'}
                </span>
              </div>
              <div className="flex space-x-3">
                <Button
                  variant="primary"
                  onClick={handleSaveShortcut}
                  disabled={!tempShortcut}
                  className="flex-1"
                >
                  Save
                </Button>
                <Button
                  variant="secondary"
                  onClick={handleCancelEdit}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Shortcut categories */}
        {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (
          <SettingsSection key={category} title={categoryLabels[category as keyof typeof categoryLabels]}>
            {shortcuts.map((shortcut) => (
              <SettingsRow
                key={shortcut.key}
                label={shortcut.label}
                description={shortcut.description}
                action={
                  <div className="flex items-center space-x-2">
                    <div className="bg-gray-700 border border-gray-600 rounded px-3 py-1 min-w-[120px] text-center">
                      <span className="font-mono text-sm text-teal-400">
                        {formatKeyDisplay(keyboardShortcuts[shortcut.key])}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleKeyRecord(shortcut.key)}
                      disabled={recordingKey !== null}
                    >
                      Edit
                    </Button>
                  </div>
                }
              />
            ))}
          </SettingsSection>
        ))}

        {/* Help text */}
        <div className="mt-6 p-4 bg-blue-900/20 border border-blue-700/30 rounded-lg">
          <h4 className="text-blue-300 font-medium mb-2">Tips:</h4>
          <ul className="text-blue-200 text-sm space-y-1">
            <li>• Click "Edit" next to any shortcut to change it</li>
            <li>• You can use combinations like Shift, Ctrl, Alt, or Cmd with other keys</li>
            <li>• Some shortcuts may conflict with browser shortcuts</li>
            <li>• Use "Reset to Defaults" to restore original shortcuts</li>
          </ul>
        </div>
      </div>
    </SettingsCard>
  );
}
