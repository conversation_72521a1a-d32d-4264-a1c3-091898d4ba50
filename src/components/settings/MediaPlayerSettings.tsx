'use client';

import React, { useState } from 'react';
import { useSettings } from './SettingsContext';
import { SettingsCard } from './SettingsCard';
import { SettingsSection } from './SettingsSection';
import { SettingsRow } from './SettingsRow';
import { Button } from '../ui/Button';
import { InfoIcon, VolumeIcon, SettingsIcon, WaveformIcon } from '../ui/Icons';

export function MediaPlayerSettings() {
  const { state, updateMediaPlayerSettings } = useSettings();
  const { mediaPlayer } = state;
  const [showEqualizerPresets, setShowEqualizerPresets] = useState(false);

  const playbackQualities = [
    { value: 'low', label: 'Low (128 kbps)', description: 'Saves bandwidth, lower quality' },
    { value: 'medium', label: 'Medium (256 kbps)', description: 'Balanced quality and size' },
    { value: 'high', label: 'High (320 kbps)', description: 'High quality, larger files' },
    { value: 'lossless', label: 'Lossless', description: 'Original quality, largest files' },
  ];

  const equalizerPresets = [
    'flat', 'rock', 'pop', 'jazz', 'classical', 'electronic', 'hip-hop', 'acoustic', 'vocal', 'bass-boost'
  ];

  const bufferSizes = [
    { value: 1024, label: '1KB (Low latency)' },
    { value: 2048, label: '2KB (Balanced)' },
    { value: 4096, label: '4KB (Recommended)' },
    { value: 8192, label: '8KB (High quality)' },
    { value: 16384, label: '16KB (Maximum)' },
  ];

  const handleVolumeChange = async (volume: number) => {
    await updateMediaPlayerSettings({ defaultVolume: volume });
  };

  const handleQualityChange = async (quality: string) => {
    await updateMediaPlayerSettings({ playbackQuality: quality as any });
  };

  const handleCrossfadeToggle = async () => {
    await updateMediaPlayerSettings({ enableCrossfade: !mediaPlayer.enableCrossfade });
  };

  const handleCrossfadeDurationChange = async (duration: number) => {
    await updateMediaPlayerSettings({ crossfadeDuration: duration });
  };

  const handleEqualizerToggle = async () => {
    await updateMediaPlayerSettings({ enableEqualizer: !mediaPlayer.enableEqualizer });
  };

  const handleEqualizerPresetChange = async (preset: string) => {
    await updateMediaPlayerSettings({ equalizerPreset: preset });
    setShowEqualizerPresets(false);
  };

  const handleReplayGainToggle = async () => {
    await updateMediaPlayerSettings({ enableReplayGain: !mediaPlayer.enableReplayGain });
  };

  const handleGaplessToggle = async () => {
    await updateMediaPlayerSettings({ enableGaplessPlayback: !mediaPlayer.enableGaplessPlayback });
  };

  const handleBufferSizeChange = async (size: number) => {
    await updateMediaPlayerSettings({ bufferSize: size });
  };

  const handleVisualizationToggle = async () => {
    await updateMediaPlayerSettings({ enableVisualization: !mediaPlayer.enableVisualization });
  };

  return (
    <SettingsCard
      title="Media Player"
      description="Configure audio playback and quality settings"
      icon={<SettingsIcon className="w-5 h-5" />}
    >
      <div className="space-y-6">
        {/* Volume Settings */}
        <SettingsSection title="Volume & Audio">
          <SettingsRow
            label="Default Volume"
            description={`Set the default volume level (${mediaPlayer.defaultVolume}%)`}
            action={
              <div className="flex items-center space-x-3">
                <VolumeIcon className="w-4 h-4 text-gray-400" />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={mediaPlayer.defaultVolume}
                  onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
                  className="w-24 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                />
                <span className="text-sm text-gray-400 w-10 text-right">
                  {mediaPlayer.defaultVolume}%
                </span>
              </div>
            }
          />

          <SettingsRow
            label="Playback Quality"
            description="Choose audio quality for playback"
            action={
              <select
                value={mediaPlayer.playbackQuality}
                onChange={(e) => handleQualityChange(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {playbackQualities.map((quality) => (
                  <option key={quality.value} value={quality.value}>
                    {quality.label}
                  </option>
                ))}
              </select>
            }
          />

          <SettingsRow
            label="Buffer Size"
            description="Audio buffer size affects latency and stability"
            action={
              <select
                value={mediaPlayer.bufferSize}
                onChange={(e) => handleBufferSizeChange(parseInt(e.target.value))}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {bufferSizes.map((buffer) => (
                  <option key={buffer.value} value={buffer.value}>
                    {buffer.label}
                  </option>
                ))}
              </select>
            }
          />
        </SettingsSection>

        {/* Crossfade Settings */}
        <SettingsSection title="Crossfade">
          <SettingsRow
            label="Enable Crossfade"
            description="Smoothly transition between tracks"
            action={
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mediaPlayer.enableCrossfade}
                  onChange={handleCrossfadeToggle}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            }
          />

          {mediaPlayer.enableCrossfade && (
            <SettingsRow
              label="Crossfade Duration"
              description={`Transition duration (${mediaPlayer.crossfadeDuration} seconds)`}
              action={
                <div className="flex items-center space-x-3">
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={mediaPlayer.crossfadeDuration}
                    onChange={(e) => handleCrossfadeDurationChange(parseInt(e.target.value))}
                    className="w-24 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <span className="text-sm text-gray-400 w-8 text-right">
                    {mediaPlayer.crossfadeDuration}s
                  </span>
                </div>
              }
            />
          )}
        </SettingsSection>

        {/* Equalizer Settings */}
        <SettingsSection title="Equalizer">
          <SettingsRow
            label="Enable Equalizer"
            description="Adjust audio frequency response"
            action={
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mediaPlayer.enableEqualizer}
                  onChange={handleEqualizerToggle}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            }
          />

          {mediaPlayer.enableEqualizer && (
            <SettingsRow
              label="Equalizer Preset"
              description="Choose a predefined equalizer setting"
              action={
                <div className="relative">
                  <Button
                    variant="secondary"
                    onClick={() => setShowEqualizerPresets(!showEqualizerPresets)}
                    className="capitalize"
                  >
                    {mediaPlayer.equalizerPreset}
                  </Button>
                  {showEqualizerPresets && (
                    <div className="absolute top-full left-0 mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10 min-w-[150px]">
                      {equalizerPresets.map((preset) => (
                        <button
                          key={preset}
                          onClick={() => handleEqualizerPresetChange(preset)}
                          className={`w-full text-left px-3 py-2 hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg capitalize ${
                            preset === mediaPlayer.equalizerPreset ? 'bg-blue-600 text-white' : 'text-gray-300'
                          }`}
                        >
                          {preset}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              }
            />
          )}
        </SettingsSection>

        {/* Advanced Settings */}
        <SettingsSection title="Advanced">
          <SettingsRow
            label="ReplayGain"
            description="Normalize volume levels across tracks"
            action={
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mediaPlayer.enableReplayGain}
                  onChange={handleReplayGainToggle}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            }
          />

          <SettingsRow
            label="Gapless Playback"
            description="Seamless playback between tracks"
            action={
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mediaPlayer.enableGaplessPlayback}
                  onChange={handleGaplessToggle}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            }
          />

          <SettingsRow
            label="Audio Visualization"
            description="Show visual effects during playback"
            action={
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={mediaPlayer.enableVisualization}
                  onChange={handleVisualizationToggle}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            }
          />
        </SettingsSection>
      </div>
    </SettingsCard>
  );
}
