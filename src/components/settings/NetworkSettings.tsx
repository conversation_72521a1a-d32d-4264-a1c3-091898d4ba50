'use client';

import React, { useState } from 'react';
import { useSettings } from './SettingsContext';

export function NetworkSettings() {
  const { state, updateNetworkSettings } = useSettings();

  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Network & Discovery</h2>
        <p className="text-gray-400 text-sm">
          Configure network media source discovery and streaming settings.
        </p>
      </div>
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-white">Auto-discover network sources</h3>
            <p className="text-sm text-gray-400">Automatically scan for UPnP/DLNA devices and network shares</p>
          </div>
          <button
            onClick={() => updateNetworkSettings({ autoDiscovery: !state.autoDiscovery })}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 focus:ring-offset-gray-900 ${
              state.autoDiscovery ? 'bg-teal-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                state.autoDiscovery ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        </div>
        
        {state.autoDiscovery && (
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-white">Scan Network</h3>
              <p className="text-sm text-gray-400">Manually scan for network devices and media servers</p>
            </div>
            <button
              className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg text-sm touch-manipulation"
            >
              Re-Scan
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
