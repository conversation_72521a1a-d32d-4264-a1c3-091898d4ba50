import React, { useState } from 'react';
import { baseClasses, spacing, typography, colors, utils, animations } from '@/lib/design-system';
import type { SettingsCardProps } from '@/types/design-system';

export function SettingsCard({
  title,
  description,
  icon,
  children,
  collapsible = false,
  defaultExpanded = true,
  className = ''
}: SettingsCardProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const cardClasses = utils.cn(
    baseClasses.card,
    'p-4 md:p-6',
    spacing.mobile.padding,
    'md:p-6',
    className
  );

  const headerClasses = utils.cn(
    'flex items-start justify-between',
    'mb-4 md:mb-6',
    collapsible && 'cursor-pointer touch-manipulation',
    collapsible && animations.transition
  );

  const titleClasses = utils.cn(
    typography.responsive.subheading,
    colors.text.primary,
    typography.weight.semibold,
    'mb-1'
  );

  const descriptionClasses = utils.cn(
    typography.responsive.body,
    colors.text.tertiary
  );

  const iconClasses = utils.cn(
    'flex-shrink-0 mt-1 mr-3',
    colors.text.secondary
  );

  const contentClasses = utils.cn(
    isExpanded ? 'block' : 'hidden',
    collapsible && animations.transition
  );

  const handleToggle = () => {
    if (collapsible) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div className={cardClasses}>
      <div
        className={headerClasses}
        onClick={handleToggle}
        role={collapsible ? 'button' : undefined}
        aria-expanded={collapsible ? isExpanded : undefined}
        tabIndex={collapsible ? 0 : undefined}
        onKeyDown={collapsible ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleToggle();
          }
        } : undefined}
      >
        <div className="flex items-start flex-1 min-w-0">
          {icon && (
            <div className={iconClasses}>
              {icon}
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h2 className={titleClasses}>
              {title}
            </h2>
            {description && (
              <p className={descriptionClasses}>
                {description}
              </p>
            )}
          </div>
        </div>

        {collapsible && (
          <button
            className={utils.cn(
              'ml-3 p-1 rounded-md',
              colors.text.tertiary,
              'hover:text-white',
              animations.transition,
              'touch-manipulation'
            )}
            aria-label={isExpanded ? 'Collapse section' : 'Expand section'}
          >
            <svg
              className={utils.cn(
                'w-5 h-5 transform transition-transform duration-200',
                isExpanded ? 'rotate-180' : 'rotate-0'
              )}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        )}
      </div>

      <div className={contentClasses}>
        {children}
      </div>
    </div>
  );
}
