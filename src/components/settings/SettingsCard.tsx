import React, { ReactNode } from 'react';

interface SettingsCardProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  children: ReactNode;
}

export function SettingsCard({ title, description, icon, children }: SettingsCardProps) {
  return (
    <div className="bg-gray-900 border border-gray-800 rounded-lg p-4 md:p-6">
      <div className="flex items-start space-x-3 mb-4 md:mb-6">
        {icon && (
          <div className="flex-shrink-0 mt-1">
            {icon}
          </div>
        )}
        <div className="flex-1 min-w-0">
          <h2 className="text-lg md:text-xl font-semibold text-white mb-1">
            {title}
          </h2>
          {description && (
            <p className="text-sm md:text-base text-gray-400">
              {description}
            </p>
          )}
        </div>
      </div>
      {children}
    </div>
  );
}
