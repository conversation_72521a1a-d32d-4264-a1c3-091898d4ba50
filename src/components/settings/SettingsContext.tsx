'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// Types
export interface MediaPlayerSettings {
  defaultVolume: number;
  playbackQuality: 'low' | 'medium' | 'high' | 'lossless';
  crossfadeDuration: number;
  enableCrossfade: boolean;
  enableEqualizer: boolean;
  equalizerPreset: string;
  enableReplayGain: boolean;
  enableGaplessPlayback: boolean;
  bufferSize: number;
  enableVisualization: boolean;
}

export interface KeyboardShortcuts {
  playPause: string;
  nextTrack: string;
  previousTrack: string;
  volumeUp: string;
  volumeDown: string;
  mute: string;
  shuffle: string;
  repeat: string;
  seekForward: string;
  seekBackward: string;
  openSettings: string;
  toggleFullscreen: string;
}

export interface SettingsState {
  helperAppUrl: string;
  musicDirs: string[];
  networkSources: any[];
  theme: 'dark' | 'light' | 'auto';
  autoDiscovery: boolean;
  mediaPlayer: MediaPlayerSettings;
  keyboardShortcuts: KeyboardShortcuts;
  loading: boolean;
  error: string | null;
  saveStatus: 'idle' | 'saving' | 'saved' | 'error';
}

export interface SettingsContextType {
  state: SettingsState;
  updateHelperAppUrl: (url: string) => Promise<void>;
  addMusicDirectory: (dir: string) => Promise<void>;
  removeMusicDirectory: (dir: string) => Promise<void>;
  updateNetworkSettings: (settings: any) => Promise<void>;
  updateAppearanceSettings: (settings: any) => Promise<void>;
  updateMediaPlayerSettings: (settings: Partial<MediaPlayerSettings>) => Promise<void>;
  updateKeyboardShortcuts: (shortcuts: Partial<KeyboardShortcuts>) => Promise<void>;
  exportSettings: () => string;
  importSettings: (settingsJson: string) => Promise<void>;
  refreshConfig: () => Promise<void>;
  clearError: () => void;
}

// Actions
type SettingsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SAVE_STATUS'; payload: SettingsState['saveStatus'] }
  | { type: 'SET_HELPER_APP_URL'; payload: string }
  | { type: 'SET_MUSIC_DIRS'; payload: string[] }
  | { type: 'SET_NETWORK_SOURCES'; payload: any[] }
  | { type: 'SET_THEME'; payload: SettingsState['theme'] }
  | { type: 'SET_AUTO_DISCOVERY'; payload: boolean }
  | { type: 'SET_MEDIA_PLAYER_SETTINGS'; payload: Partial<MediaPlayerSettings> }
  | { type: 'SET_KEYBOARD_SHORTCUTS'; payload: Partial<KeyboardShortcuts> }
  | { type: 'LOAD_CONFIG'; payload: Partial<SettingsState> };

// Initial state
const initialState: SettingsState = {
  helperAppUrl: 'http://localhost:3001',
  musicDirs: [],
  networkSources: [],
  theme: 'dark',
  autoDiscovery: true,
  mediaPlayer: {
    defaultVolume: 75,
    playbackQuality: 'high',
    crossfadeDuration: 3,
    enableCrossfade: false,
    enableEqualizer: false,
    equalizerPreset: 'flat',
    enableReplayGain: false,
    enableGaplessPlayback: true,
    bufferSize: 4096,
    enableVisualization: true,
  },
  keyboardShortcuts: {
    playPause: 'Space',
    nextTrack: 'ArrowRight',
    previousTrack: 'ArrowLeft',
    volumeUp: 'ArrowUp',
    volumeDown: 'ArrowDown',
    mute: 'KeyM',
    shuffle: 'KeyS',
    repeat: 'KeyR',
    seekForward: 'Shift+ArrowRight',
    seekBackward: 'Shift+ArrowLeft',
    openSettings: 'Comma',
    toggleFullscreen: 'KeyF',
  },
  loading: false,
  error: null,
  saveStatus: 'idle',
};

// Reducer
function settingsReducer(state: SettingsState, action: SettingsAction): SettingsState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_SAVE_STATUS':
      return { ...state, saveStatus: action.payload };
    case 'SET_HELPER_APP_URL':
      return { ...state, helperAppUrl: action.payload };
    case 'SET_MUSIC_DIRS':
      return { ...state, musicDirs: action.payload };
    case 'SET_NETWORK_SOURCES':
      return { ...state, networkSources: action.payload };
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'SET_AUTO_DISCOVERY':
      return { ...state, autoDiscovery: action.payload };
    case 'SET_MEDIA_PLAYER_SETTINGS':
      return { ...state, mediaPlayer: { ...state.mediaPlayer, ...action.payload } };
    case 'SET_KEYBOARD_SHORTCUTS':
      return { ...state, keyboardShortcuts: { ...state.keyboardShortcuts, ...action.payload } };
    case 'LOAD_CONFIG':
      return { ...state, ...action.payload, loading: false };
    default:
      return state;
  }
}

// Context
const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

// Provider
export function SettingsProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(settingsReducer, initialState);

  // API functions
  const fetchConfig = async (url?: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const helperUrl = url || state.helperAppUrl;
      const response = await fetch(`${helperUrl}/api/config`);
      if (!response.ok) throw new Error('Failed to fetch config');
      const data = await response.json();
      dispatch({ type: 'LOAD_CONFIG', payload: { musicDirs: data.musicDirs || [] } });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  };

  const saveConfig = async (config: any, url?: string) => {
    try {
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saving' });
      const helperUrl = url || state.helperAppUrl;
      const response = await fetch(`${helperUrl}/api/config`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config),
      });
      if (!response.ok) throw new Error('Failed to save config');
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saved' });
      setTimeout(() => dispatch({ type: 'SET_SAVE_STATUS', payload: 'idle' }), 2000);
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'error' });
    }
  };

  // Context methods
  const updateHelperAppUrl = async (url: string) => {
    dispatch({ type: 'SET_HELPER_APP_URL', payload: url });
    // Save to localStorage for persistence
    localStorage.setItem('helperAppUrl', url);
  };

  const addMusicDirectory = async (dir: string) => {
    if (!dir.trim() || state.musicDirs.includes(dir)) return;
    const newDirs = [...state.musicDirs, dir];
    dispatch({ type: 'SET_MUSIC_DIRS', payload: newDirs });
    await saveConfig({ musicDirs: newDirs }, state.helperAppUrl);
  };

  const removeMusicDirectory = async (dir: string) => {
    const newDirs = state.musicDirs.filter(d => d !== dir);
    dispatch({ type: 'SET_MUSIC_DIRS', payload: newDirs });
    await saveConfig({ musicDirs: newDirs }, state.helperAppUrl);
  };

  const updateNetworkSettings = async (settings: any) => {
    if (settings.autoDiscovery !== undefined) {
      dispatch({ type: 'SET_AUTO_DISCOVERY', payload: settings.autoDiscovery });
      localStorage.setItem('autoDiscovery', settings.autoDiscovery.toString());
    }
    if (settings.networkSources) {
      dispatch({ type: 'SET_NETWORK_SOURCES', payload: settings.networkSources });
      localStorage.setItem('networkSources', JSON.stringify(settings.networkSources));
    }
  };

  const updateAppearanceSettings = async (settings: any) => {
    if (settings.theme) {
      dispatch({ type: 'SET_THEME', payload: settings.theme });
      // Save to localStorage for persistence
      localStorage.setItem('theme', settings.theme);
    }
  };

  const updateMediaPlayerSettings = async (settings: Partial<MediaPlayerSettings>) => {
    dispatch({ type: 'SET_MEDIA_PLAYER_SETTINGS', payload: settings });
    // Save to localStorage for persistence
    const currentSettings = JSON.parse(localStorage.getItem('mediaPlayerSettings') || '{}');
    const updatedSettings = { ...currentSettings, ...settings };
    localStorage.setItem('mediaPlayerSettings', JSON.stringify(updatedSettings));
  };

  const updateKeyboardShortcuts = async (shortcuts: Partial<KeyboardShortcuts>) => {
    dispatch({ type: 'SET_KEYBOARD_SHORTCUTS', payload: shortcuts });
    // Save to localStorage for persistence
    const currentShortcuts = JSON.parse(localStorage.getItem('keyboardShortcuts') || '{}');
    const updatedShortcuts = { ...currentShortcuts, ...shortcuts };
    localStorage.setItem('keyboardShortcuts', JSON.stringify(updatedShortcuts));
  };

  const exportSettings = (): string => {
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: {
        helperAppUrl: state.helperAppUrl,
        theme: state.theme,
        autoDiscovery: state.autoDiscovery,
        networkSources: state.networkSources,
        mediaPlayer: state.mediaPlayer,
        keyboardShortcuts: state.keyboardShortcuts,
        // Note: musicDirs are not exported as they are device-specific
      }
    };
    return JSON.stringify(exportData, null, 2);
  };

  const importSettings = async (settingsJson: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const importData = JSON.parse(settingsJson);

      // Validate import data structure
      if (!importData.settings || !importData.version) {
        throw new Error('Invalid settings file format');
      }

      const { settings } = importData;

      // Import each setting category
      if (settings.helperAppUrl) {
        await updateHelperAppUrl(settings.helperAppUrl);
      }

      if (settings.theme) {
        await updateAppearanceSettings({ theme: settings.theme });
      }

      if (settings.autoDiscovery !== undefined || settings.networkSources) {
        await updateNetworkSettings({
          autoDiscovery: settings.autoDiscovery,
          networkSources: settings.networkSources
        });
      }

      if (settings.mediaPlayer) {
        await updateMediaPlayerSettings(settings.mediaPlayer);
      }

      if (settings.keyboardShortcuts) {
        await updateKeyboardShortcuts(settings.keyboardShortcuts);
      }

      dispatch({ type: 'SET_LOADING', payload: false });
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saved' });
      setTimeout(() => dispatch({ type: 'SET_SAVE_STATUS', payload: 'idle' }), 2000);
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: `Failed to import settings: ${(error as Error).message}` });
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  };

  const refreshConfig = async () => {
    await fetchConfig();
  };

  const clearError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  // Load initial config
  useEffect(() => {
    const savedUrl = localStorage.getItem('helperAppUrl');
    const savedTheme = localStorage.getItem('theme') as 'dark' | 'light' | 'auto' | null;
    const savedAutoDiscovery = localStorage.getItem('autoDiscovery');
    const savedNetworkSources = localStorage.getItem('networkSources');
    const savedMediaPlayerSettings = localStorage.getItem('mediaPlayerSettings');
    const savedKeyboardShortcuts = localStorage.getItem('keyboardShortcuts');

    if (savedUrl) {
      dispatch({ type: 'SET_HELPER_APP_URL', payload: savedUrl });
      fetchConfig(savedUrl);
    } else {
      fetchConfig();
    }

    if (savedTheme) {
      dispatch({ type: 'SET_THEME', payload: savedTheme });
    }

    if (savedAutoDiscovery) {
      dispatch({ type: 'SET_AUTO_DISCOVERY', payload: savedAutoDiscovery === 'true' });
    }

    if (savedNetworkSources) {
      try {
        const sources = JSON.parse(savedNetworkSources);
        dispatch({ type: 'SET_NETWORK_SOURCES', payload: sources });
      } catch (error) {
        console.error('Failed to parse saved network sources:', error);
      }
    }

    if (savedMediaPlayerSettings) {
      try {
        const settings = JSON.parse(savedMediaPlayerSettings);
        dispatch({ type: 'SET_MEDIA_PLAYER_SETTINGS', payload: settings });
      } catch (error) {
        console.error('Failed to parse saved media player settings:', error);
      }
    }

    if (savedKeyboardShortcuts) {
      try {
        const shortcuts = JSON.parse(savedKeyboardShortcuts);
        dispatch({ type: 'SET_KEYBOARD_SHORTCUTS', payload: shortcuts });
      } catch (error) {
        console.error('Failed to parse saved keyboard shortcuts:', error);
      }
    }
  }, []);

  const contextValue: SettingsContextType = {
    state,
    updateHelperAppUrl,
    addMusicDirectory,
    removeMusicDirectory,
    updateNetworkSettings,
    updateAppearanceSettings,
    updateMediaPlayerSettings,
    updateKeyboardShortcuts,
    exportSettings,
    importSettings,
    refreshConfig,
    clearError,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook
export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}
