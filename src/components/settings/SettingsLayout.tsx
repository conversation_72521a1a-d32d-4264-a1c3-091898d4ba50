'use client';

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { useSettings } from './SettingsContext';

interface SettingsLayoutProps {
  children: ReactNode;
}

export function SettingsLayout({ children }: SettingsLayoutProps) {
  const { state, clearError } = useSettings();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="bg-gray-900 border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 md:h-16">
            <div className="flex items-center space-x-2 md:space-x-4">
              <Link
                href="/"
                className="text-teal-400 hover:text-teal-300 transition-colors touch-manipulation p-2 md:p-0"
              >
                <span className="hidden md:inline">← Back to Player</span>
                <svg className="w-6 h-6 md:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <h1 className="text-lg md:text-xl font-semibold">Settings</h1>
            </div>
            
            {/* Save Status Indicator */}
            {state.saveStatus !== 'idle' && (
              <div className="flex items-center space-x-2">
                {state.saveStatus === 'saving' && (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-400"></div>
                    <span className="text-sm text-gray-400">Saving...</span>
                  </>
                )}
                {state.saveStatus === 'saved' && (
                  <>
                    <div className="h-4 w-4 text-green-400">✓</div>
                    <span className="text-sm text-green-400">Saved</span>
                  </>
                )}
                {state.saveStatus === 'error' && (
                  <>
                    <div className="h-4 w-4 text-red-400">✗</div>
                    <span className="text-sm text-red-400">Save failed</span>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Error Banner */}
      {state.error && (
        <div className="bg-red-900 border-l-4 border-red-500 p-4">
          <div className="flex items-center justify-between">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-300">{state.error}</p>
              </div>
            </div>
            <button
              onClick={clearError}
              className="text-red-400 hover:text-red-300"
            >
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8">
        {state.loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-400"></div>
            <span className="ml-3 text-gray-400">Loading settings...</span>
          </div>
        ) : (
          children
        )}
      </main>
    </div>
  );
}
