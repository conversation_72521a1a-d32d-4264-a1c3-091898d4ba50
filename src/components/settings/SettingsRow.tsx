import React, { ReactNode } from 'react';

interface SettingsRowProps {
  label: string;
  description?: string;
  action: ReactNode;
}

export function SettingsRow({ label, description, action }: SettingsRowProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0 md:space-x-4 p-3 md:p-4 bg-gray-800 rounded-lg">
      <div className="flex-1 min-w-0">
        <div className="text-sm md:text-base font-medium text-white">
          {label}
        </div>
        {description && (
          <div className="text-xs md:text-sm text-gray-400 mt-1">
            {description}
          </div>
        )}
      </div>
      <div className="flex-shrink-0">
        {action}
      </div>
    </div>
  );
}
