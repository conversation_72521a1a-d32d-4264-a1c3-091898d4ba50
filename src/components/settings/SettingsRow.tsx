import React from 'react';
import { spacing, typography, colors, utils, baseClasses } from '@/lib/design-system';
import type { SettingsRowProps } from '@/types/design-system';

export function SettingsRow({
  label,
  description,
  action,
  layout = 'auto',
  spacing: spacingVariant = 'normal',
  className = '',
  children
}: SettingsRowProps) {
  const spacingMap = {
    compact: 'p-2 md:p-3',
    normal: 'p-3 md:p-4',
    relaxed: 'p-4 md:p-5'
  };

  const layoutMap = {
    horizontal: 'flex-row items-center justify-between space-x-4',
    vertical: 'flex-col space-y-3',
    auto: 'flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0 md:space-x-4'
  };

  const rowClasses = utils.cn(
    'flex rounded-lg',
    colors.background.secondary,
    spacingMap[spacingVariant],
    layoutMap[layout],
    // Touch-friendly on mobile
    'touch-manipulation',
    className
  );

  const labelClasses = utils.cn(
    typography.responsive.body,
    typography.weight.medium,
    colors.text.primary
  );

  const descriptionClasses = utils.cn(
    typography.text.xs,
    'md:text-sm',
    colors.text.tertiary,
    'mt-1'
  );

  const contentClasses = utils.cn(
    'flex-1 min-w-0'
  );

  const actionClasses = utils.cn(
    'flex-shrink-0',
    // Ensure proper spacing on mobile
    layout === 'auto' && 'w-full md:w-auto',
    layout === 'vertical' && 'w-full'
  );

  return (
    <div className={rowClasses}>
      <div className={contentClasses}>
        <div className={labelClasses}>
          {label}
        </div>
        {description && (
          <div className={descriptionClasses}>
            {description}
          </div>
        )}
        {children && (
          <div className="mt-2">
            {children}
          </div>
        )}
      </div>
      <div className={actionClasses}>
        {action}
      </div>
    </div>
  );
}
