import React, { ReactNode } from 'react';

interface SettingsSectionProps {
  title: string;
  children: ReactNode;
}

export function SettingsSection({ title, children }: SettingsSectionProps) {
  return (
    <div className="space-y-3 md:space-y-4">
      <h3 className="text-base md:text-lg font-medium text-white border-b border-gray-700 pb-2">
        {title}
      </h3>
      <div className="space-y-3 md:space-y-4">
        {children}
      </div>
    </div>
  );
}
