import React from 'react';
import { variants, sizing, animations, utils } from '@/lib/design-system';
import type { ButtonProps } from '@/types/design-system';

export function Button({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  icon,
  className = '',
  children,
  disabled,
  ...props
}: ButtonProps) {
  const baseClasses = utils.cn(
    // Base styles
    'inline-flex items-center justify-center font-medium rounded-lg',
    // Touch-friendly
    'touch-manipulation select-none',
    // Transitions and focus
    animations.transition,
    animations.focus,
    // Disabled state
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    // Full width option
    fullWidth && 'w-full',
    // Loading state
    loading && 'cursor-wait'
  );

  const variantClasses = variants.button[variant];
  const sizeClasses = sizing.button[size];

  const classes = utils.cn(
    baseClasses,
    variantClasses,
    sizeClasses,
    className
  );

  const isDisabled = disabled || loading;

  return (
    <button
      className={classes}
      disabled={isDisabled}
      {...props}
    >
      {loading && (
        <div className="mr-2 animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
      )}
      {!loading && icon && (
        <span className="mr-2 flex-shrink-0">
          {icon}
        </span>
      )}
      <span className={utils.cn(
        'truncate',
        loading && 'opacity-75'
      )}>
        {children}
      </span>
    </button>
  );
}
