'use client';

import React, { useState, useEffect } from 'react';
import { Button } from './Button';
import { Input } from './Input';
import { utils, colors, typography, spacing, animations } from '@/lib/design-system';
import type { DirectoryBrowserProps, DirectoryItem } from '@/types/design-system';

// Modal wrapper for the directory browser
export function DirectoryBrowserModal({
  isOpen,
  onClose,
  onSelect,
  ...props
}: DirectoryBrowserProps & { isOpen: boolean; onClose: () => void }) {
  if (!isOpen) return null;

  const handleSelect = (path: string) => {
    onSelect(path);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleCancel}
      />

      {/* Modal Content */}
      <div className="relative z-10 w-full max-w-2xl">
        <DirectoryBrowser
          onSelect={handleSelect}
          onCancel={handleCancel}
          {...props}
        />
      </div>
    </div>
  );
}

export function DirectoryBrowser({
  onSelect,
  onCancel,
  initialPath = '',
  mode = 'directory',
  multiple = false,
  className = ''
}: DirectoryBrowserProps) {
  const [currentPath, setCurrentPath] = useState(initialPath || '/');
  const [items, setItems] = useState<DirectoryItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [manualPath, setManualPath] = useState(currentPath);

  // Mock directory listing - in a real app, this would call an API
  const fetchDirectoryContents = async (path: string): Promise<DirectoryItem[]> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock data - replace with actual API call
    const mockItems: DirectoryItem[] = [
      {
        name: '..',
        path: path === '/' ? '/' : path.split('/').slice(0, -1).join('/') || '/',
        type: 'directory'
      },
      {
        name: 'Documents',
        path: `${path}/Documents`,
        type: 'directory',
        permissions: { read: true, write: true, execute: true }
      },
      {
        name: 'Music',
        path: `${path}/Music`,
        type: 'directory',
        permissions: { read: true, write: true, execute: true }
      },
      {
        name: 'Pictures',
        path: `${path}/Pictures`,
        type: 'directory',
        permissions: { read: true, write: false, execute: true }
      },
      {
        name: 'Videos',
        path: `${path}/Videos`,
        type: 'directory',
        permissions: { read: true, write: true, execute: true }
      }
    ];

    return mockItems.filter(item => {
      if (mode === 'directory') return item.type === 'directory';
      if (mode === 'file') return item.type === 'file';
      return true;
    });
  };

  const loadDirectory = async (path: string) => {
    setLoading(true);
    setError(null);
    try {
      const contents = await fetchDirectoryContents(path);
      setItems(contents);
      setCurrentPath(path);
      setManualPath(path);
    } catch (err) {
      setError('Failed to load directory contents');
      console.error('Directory loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDirectory(currentPath);
  }, []);

  const handleItemClick = (item: DirectoryItem) => {
    if (item.type === 'directory') {
      loadDirectory(item.path);
    } else if (mode !== 'directory') {
      if (multiple) {
        setSelectedItems(prev => 
          prev.includes(item.path) 
            ? prev.filter(p => p !== item.path)
            : [...prev, item.path]
        );
      } else {
        setSelectedItems([item.path]);
      }
    }
  };

  const handleSelect = () => {
    if (mode === 'directory') {
      onSelect(currentPath);
    } else {
      if (multiple) {
        onSelect(selectedItems.join(','));
      } else {
        onSelect(selectedItems[0] || '');
      }
    }
  };

  const handleManualPathSubmit = () => {
    if (manualPath.trim()) {
      loadDirectory(manualPath.trim());
    }
  };

  const validatePath = (path: string): { isValid: boolean; error?: string } => {
    if (!path.trim()) return { isValid: false, error: 'Path cannot be empty' };
    if (path === '/') return { isValid: false, error: 'Root directory access is restricted for security' };
    if (path.includes('..')) return { isValid: false, error: 'Relative paths are not allowed' };
    return { isValid: true };
  };

  const pathValidation = validatePath(currentPath);

  const containerClasses = utils.cn(
    'bg-gray-800 border border-gray-700 rounded-lg p-4 max-w-2xl w-full max-h-[80vh] flex flex-col',
    className
  );

  const headerClasses = utils.cn(
    'flex flex-col space-y-3 mb-4 pb-4 border-b border-gray-700'
  );

  const pathInputClasses = utils.cn(
    'flex space-x-2'
  );

  const itemsContainerClasses = utils.cn(
    'flex-1 overflow-y-auto space-y-1 mb-4 min-h-[200px] max-h-[400px]'
  );

  const itemClasses = (item: DirectoryItem, isSelected: boolean) => utils.cn(
    'flex items-center space-x-3 p-3 rounded-md cursor-pointer transition-colors',
    'hover:bg-gray-700',
    isSelected && 'bg-blue-600 hover:bg-blue-700',
    !item.permissions?.read && 'opacity-50 cursor-not-allowed'
  );

  const footerClasses = utils.cn(
    'flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-700'
  );

  return (
    <div className={containerClasses}>
      {/* Header */}
      <div className={headerClasses}>
        <h3 className={utils.cn(typography.text.lg, typography.weight.semibold, colors.text.primary)}>
          {mode === 'directory' ? 'Select Directory' : 'Select Files'}
        </h3>
        
        {/* Manual Path Input */}
        <div className={pathInputClasses}>
          <Input
            value={manualPath}
            onChange={(e) => setManualPath(e.target.value)}
            placeholder="Enter path manually"
            className="flex-1"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleManualPathSubmit();
              }
            }}
          />
          <Button
            variant="secondary"
            onClick={handleManualPathSubmit}
            disabled={loading}
          >
            Go
          </Button>
        </div>

        {/* Current Path Display */}
        <div className={utils.cn(typography.text.sm, colors.text.tertiary)}>
          Current: {currentPath}
        </div>

        {/* Path Validation Error */}
        {!pathValidation.isValid && (
          <div className={utils.cn(typography.text.sm, 'text-red-400 flex items-center')}>
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {pathValidation.error}
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-500"></div>
          <span className={utils.cn('ml-3', colors.text.secondary)}>Loading...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className={utils.cn('text-red-400 p-4 bg-red-900 bg-opacity-20 rounded-md mb-4')}>
          {error}
        </div>
      )}

      {/* Directory Items */}
      {!loading && !error && (
        <div className={itemsContainerClasses}>
          {items.map((item) => {
            const isSelected = selectedItems.includes(item.path);
            const canAccess = item.permissions?.read !== false;
            
            return (
              <div
                key={item.path}
                className={itemClasses(item, isSelected)}
                onClick={() => canAccess && handleItemClick(item)}
              >
                {/* Icon */}
                <div className="flex-shrink-0">
                  {item.type === 'directory' ? (
                    <svg className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  )}
                </div>

                {/* Name */}
                <div className="flex-1 min-w-0">
                  <div className={utils.cn(
                    typography.text.sm,
                    colors.text.primary,
                    'truncate'
                  )}>
                    {item.name}
                  </div>
                  {!canAccess && (
                    <div className={utils.cn(typography.text.xs, 'text-red-400')}>
                      No read permission
                    </div>
                  )}
                </div>

                {/* Selection indicator */}
                {isSelected && mode !== 'directory' && (
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* Footer */}
      <div className={footerClasses}>
        <Button
          variant="ghost"
          onClick={onCancel}
          className="flex-1 sm:flex-none"
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSelect}
          disabled={
            loading || 
            !pathValidation.isValid || 
            (mode !== 'directory' && selectedItems.length === 0)
          }
          className="flex-1 sm:flex-none"
        >
          Select {mode === 'directory' ? 'Directory' : `${selectedItems.length} Item${selectedItems.length !== 1 ? 's' : ''}`}
        </Button>
      </div>
    </div>
  );
}
