import React, { forwardRef } from 'react';
import { variants, sizing, animations, utils, colors, typography } from '@/lib/design-system';
import type { InputProps } from '@/types/design-system';

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  description,
  error,
  size = 'md',
  fullWidth = false,
  icon,
  rightIcon,
  className = '',
  id,
  ...props
}, ref) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const descriptionId = description ? `${inputId}-description` : undefined;
  const errorId = error ? `${inputId}-error` : undefined;

  const baseClasses = utils.cn(
    // Base styles
    'rounded-md border w-full',
    // Touch-friendly
    'touch-manipulation',
    // Transitions and focus
    animations.transition,
    animations.focus,
    // Typography
    typography.text.sm,
    // Disabled state
    'disabled:opacity-50 disabled:cursor-not-allowed',
    // Full width
    fullWidth && 'w-full'
  );

  const variantClasses = error ? variants.input.error : variants.input.default;
  const sizeClasses = sizing.input[size];

  const inputClasses = utils.cn(
    baseClasses,
    variantClasses,
    sizeClasses,
    // Icon padding adjustments
    icon && 'pl-10',
    rightIcon && 'pr-10',
    className
  );

  const labelClasses = utils.cn(
    'block font-medium mb-2',
    typography.text.sm,
    colors.text.primary,
    'md:text-base'
  );

  const descriptionClasses = utils.cn(
    'mt-1',
    typography.text.xs,
    colors.text.tertiary,
    'md:text-sm'
  );

  const errorClasses = utils.cn(
    'mt-1 flex items-center',
    typography.text.xs,
    'text-red-400',
    'md:text-sm'
  );

  return (
    <div className={utils.cn('w-full', fullWidth && 'flex-1')}>
      {label && (
        <label htmlFor={inputId} className={labelClasses}>
          {label}
          {props.required && (
            <span className="ml-1 text-red-400" aria-label="required">
              *
            </span>
          )}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className={utils.cn(
              'text-gray-400',
              error && 'text-red-400'
            )}>
              {icon}
            </span>
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={inputClasses}
          aria-describedby={utils.cn(
            descriptionId,
            errorId
          ).trim() || undefined}
          aria-invalid={error ? 'true' : undefined}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className={utils.cn(
              'text-gray-400',
              error && 'text-red-400'
            )}>
              {rightIcon}
            </span>
          </div>
        )}
      </div>
      
      {description && !error && (
        <p id={descriptionId} className={descriptionClasses}>
          {description}
        </p>
      )}
      
      {error && (
        <p id={errorId} className={errorClasses}>
          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';
