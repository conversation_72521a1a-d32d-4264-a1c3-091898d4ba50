'use client';

import React from 'react';
import { InfoPopup } from './InfoPopup';

interface AudioFormat {
  extension: string;
  name: string;
  description: string;
  quality: 'Lossless' | 'High' | 'Medium' | 'Variable';
}

const audioFormats: AudioFormat[] = [
  {
    extension: 'MP3',
    name: 'MPEG Audio Layer III',
    description: 'Most common compressed audio format',
    quality: 'Variable'
  },
  {
    extension: 'FLAC',
    name: 'Free Lossless Audio Codec',
    description: 'Lossless compression, perfect quality',
    quality: 'Lossless'
  },
  {
    extension: 'WAV',
    name: 'Waveform Audio File Format',
    description: 'Uncompressed audio, large file size',
    quality: 'Lossless'
  },
  {
    extension: 'OGG',
    name: 'Ogg Vorbis',
    description: 'Open-source compressed format',
    quality: 'High'
  },
  {
    extension: 'M4A',
    name: 'MPEG-4 Audio',
    description: 'Apple\'s preferred audio format',
    quality: 'High'
  },
  {
    extension: 'AAC',
    name: 'Advanced Audio Coding',
    description: 'High-quality compressed format',
    quality: 'High'
  },
  {
    extension: 'OPUS',
    name: 'Opus Audio Codec',
    description: 'Modern, efficient compression',
    quality: 'High'
  },
  {
    extension: 'AIFF',
    name: 'Audio Interchange File Format',
    description: 'Apple\'s uncompressed format',
    quality: 'Lossless'
  },
  {
    extension: 'WMA',
    name: 'Windows Media Audio',
    description: 'Microsoft\'s audio format',
    quality: 'Medium'
  },
  {
    extension: 'APE',
    name: 'Monkey\'s Audio',
    description: 'Lossless compression format',
    quality: 'Lossless'
  },
  {
    extension: 'ALAC',
    name: 'Apple Lossless Audio Codec',
    description: 'Apple\'s lossless format',
    quality: 'Lossless'
  },
  {
    extension: 'DSD',
    name: 'Direct Stream Digital',
    description: 'High-resolution audio format',
    quality: 'Lossless'
  }
];

const qualityColors = {
  'Lossless': 'text-green-400',
  'High': 'text-blue-400',
  'Medium': 'text-yellow-400',
  'Variable': 'text-purple-400'
};

interface SupportedFormatsPopupProps {
  className?: string;
}

export function SupportedFormatsPopup({ className }: SupportedFormatsPopupProps) {
  return (
    <InfoPopup title="Supported Audio Formats" className={className}>
      <div className="space-y-4">
        <p className="text-sm text-gray-300">
          Wayne's Media Player supports a wide range of audio formats for maximum compatibility:
        </p>
        
        {/* Format List */}
        <div className="space-y-2 max-h-48 md:max-h-64 overflow-y-auto">
          {audioFormats.map((format) => (
            <div
              key={format.extension}
              className="flex items-start space-x-2 md:space-x-3 p-2 md:p-3 bg-gray-700 rounded-md"
            >
              <div className="flex-shrink-0">
                <span className="inline-block px-2 py-1 text-xs font-mono font-semibold bg-gray-600 text-white rounded">
                  {format.extension}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-xs md:text-sm font-medium text-white truncate">
                    {format.name}
                  </h4>
                  <span className={`text-xs font-medium ${qualityColors[format.quality]}`}>
                    {format.quality}
                  </span>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {format.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Quality Legend */}
        <div className="border-t border-gray-700 pt-3">
          <h4 className="text-xs md:text-sm font-medium text-white mb-2">Quality Levels:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></span>
              <span className="text-gray-300">Lossless - Perfect quality</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"></span>
              <span className="text-gray-300">High - Near-perfect</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></span>
              <span className="text-gray-300">Medium - Good quality</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-purple-400 rounded-full flex-shrink-0"></span>
              <span className="text-gray-300">Variable - Depends on bitrate</span>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="bg-blue-900 bg-opacity-50 rounded-md p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-4 w-4 md:h-5 md:w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-2 md:ml-3">
              <h3 className="text-xs md:text-sm font-medium text-blue-300">Note</h3>
              <div className="mt-1 md:mt-2 text-xs md:text-sm text-blue-200">
                <p>
                  Format support depends on your browser's capabilities. Most modern browsers support MP3, WAV, OGG, and M4A natively.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </InfoPopup>
  );
}
