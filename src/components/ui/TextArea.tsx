import React, { forwardRef } from 'react';
import { variants, animations, utils, colors, typography } from '@/lib/design-system';

interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  description?: string;
  error?: string;
  fullWidth?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({
  label,
  description,
  error,
  fullWidth = false,
  resize = 'vertical',
  className = '',
  id,
  rows = 4,
  ...props
}, ref) => {
  const textAreaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
  const descriptionId = description ? `${textAreaId}-description` : undefined;
  const errorId = error ? `${textAreaId}-error` : undefined;

  const baseClasses = utils.cn(
    // Base styles
    'rounded-md border w-full px-3 py-2.5',
    // Touch-friendly
    'touch-manipulation',
    // Transitions and focus
    animations.transition,
    animations.focus,
    // Typography
    typography.text.sm,
    'md:text-base',
    // Resize behavior
    resize === 'none' && 'resize-none',
    resize === 'vertical' && 'resize-y',
    resize === 'horizontal' && 'resize-x',
    resize === 'both' && 'resize',
    // Disabled state
    'disabled:opacity-50 disabled:cursor-not-allowed',
    // Full width
    fullWidth && 'w-full',
    // Mobile optimizations
    'min-h-[100px] md:min-h-[120px]'
  );

  const variantClasses = error ? variants.input.error : variants.input.default;

  const textAreaClasses = utils.cn(
    baseClasses,
    variantClasses,
    className
  );

  const labelClasses = utils.cn(
    'block font-medium mb-2',
    typography.text.sm,
    colors.text.primary,
    'md:text-base'
  );

  const descriptionClasses = utils.cn(
    'mt-1',
    typography.text.xs,
    colors.text.tertiary,
    'md:text-sm'
  );

  const errorClasses = utils.cn(
    'mt-1 flex items-start',
    typography.text.xs,
    'text-red-400',
    'md:text-sm'
  );

  return (
    <div className={utils.cn('w-full', fullWidth && 'flex-1')}>
      {label && (
        <label htmlFor={textAreaId} className={labelClasses}>
          {label}
          {props.required && (
            <span className="ml-1 text-red-400" aria-label="required">
              *
            </span>
          )}
        </label>
      )}
      
      <textarea
        ref={ref}
        id={textAreaId}
        rows={rows}
        className={textAreaClasses}
        aria-describedby={utils.cn(
          descriptionId,
          errorId
        ).trim() || undefined}
        aria-invalid={error ? 'true' : undefined}
        {...props}
      />
      
      {description && !error && (
        <p id={descriptionId} className={descriptionClasses}>
          {description}
        </p>
      )}
      
      {error && (
        <p id={errorId} className={errorClasses}>
          <svg className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
});

TextArea.displayName = 'TextArea';
