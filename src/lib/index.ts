/**
 * Design System Library Exports
 * Central export point for all design system utilities and types
 */

// Design system utilities and constants
export * from './design-system';

// Type definitions
export * from '../types/design-system';

// Re-export commonly used utilities with shorter names
export { utils as ds } from './design-system';
export { baseClasses as base } from './design-system';
export { variants } from './design-system';
export { colors } from './design-system';
export { typography as text } from './design-system';
export { spacing } from './design-system';
export { sizing } from './design-system';
export { animations } from './design-system';
export { layout } from './design-system';

// Utility functions for common patterns
export const createResponsiveClass = (mobile: string, desktop?: string): string => {
  return desktop ? `${mobile} md:${desktop}` : mobile;
};

export const createTouchFriendlyClass = (baseClass: string): string => {
  return `${baseClass} touch-manipulation select-none`;
};

export const combineClasses = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

// Common component class builders
export const buildButtonClasses = (
  variant: 'primary' | 'secondary' | 'ghost' | 'danger' = 'primary',
  size: 'sm' | 'md' | 'lg' = 'md',
  fullWidth = false,
  loading = false
) => {
  const { variants, sizing, animations, utils } = require('./design-system');
  
  return utils.cn(
    'inline-flex items-center justify-center font-medium rounded-lg',
    'touch-manipulation select-none',
    animations.transition,
    animations.focus,
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    variants.button[variant],
    sizing.button[size],
    fullWidth && 'w-full',
    loading && 'cursor-wait'
  );
};

export const buildInputClasses = (
  hasError = false,
  size: 'sm' | 'md' | 'lg' = 'md',
  fullWidth = false
) => {
  const { variants, sizing, animations, utils } = require('./design-system');
  
  return utils.cn(
    'rounded-md border w-full',
    'touch-manipulation',
    animations.transition,
    animations.focus,
    'disabled:opacity-50 disabled:cursor-not-allowed',
    hasError ? variants.input.error : variants.input.default,
    sizing.input[size],
    fullWidth && 'w-full'
  );
};

export const buildCardClasses = (padding: 'sm' | 'md' | 'lg' = 'md') => {
  const { baseClasses, spacing, utils } = require('./design-system');
  
  const paddingMap = {
    sm: 'p-3 md:p-4',
    md: 'p-4 md:p-6',
    lg: 'p-6 md:p-8'
  };
  
  return utils.cn(
    baseClasses.card,
    paddingMap[padding]
  );
};

// Responsive breakpoint helpers
export const breakpointHelpers = {
  isMobile: () => window.innerWidth < 768,
  isTablet: () => window.innerWidth >= 768 && window.innerWidth < 1024,
  isDesktop: () => window.innerWidth >= 1024,
  
  // Media query strings for CSS-in-JS
  mobile: '@media (max-width: 767px)',
  tablet: '@media (min-width: 768px) and (max-width: 1023px)',
  desktop: '@media (min-width: 1024px)',
  
  // Tailwind breakpoint classes
  sm: 'sm:',
  md: 'md:',
  lg: 'lg:',
  xl: 'xl:',
  '2xl': '2xl:'
};

// Theme helpers
export const themeHelpers = {
  isDarkMode: () => {
    if (typeof window === 'undefined') return true;
    return document.documentElement.classList.contains('dark');
  },
  
  toggleTheme: () => {
    if (typeof window === 'undefined') return;
    document.documentElement.classList.toggle('dark');
  },
  
  setTheme: (theme: 'light' | 'dark') => {
    if (typeof window === 'undefined') return;
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
  }
};

// Accessibility helpers
export const a11yHelpers = {
  // Generate unique IDs for form elements
  generateId: (prefix = 'element') => `${prefix}-${Math.random().toString(36).substr(2, 9)}`,
  
  // ARIA attributes for common patterns
  expandableButton: (isExpanded: boolean) => ({
    'aria-expanded': isExpanded,
    'aria-haspopup': 'true'
  }),
  
  formField: (id: string, hasError = false, hasDescription = false) => ({
    'aria-invalid': hasError ? 'true' : undefined,
    'aria-describedby': hasDescription ? `${id}-description` : undefined
  }),
  
  // Focus management
  trapFocus: (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };
    
    element.addEventListener('keydown', handleTabKey);
    firstElement?.focus();
    
    return () => element.removeEventListener('keydown', handleTabKey);
  }
};

// Animation helpers
export const animationHelpers = {
  // CSS transition classes
  transition: 'transition-all duration-200 ease-in-out',
  fadeIn: 'animate-in fade-in duration-200',
  fadeOut: 'animate-out fade-out duration-200',
  slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
  slideOut: 'animate-out slide-out-to-bottom-4 duration-300',
  
  // Reduced motion support
  respectsReducedMotion: (animationClass: string) => {
    return `motion-safe:${animationClass}`;
  }
};

// Touch interaction helpers
export const touchHelpers = {
  // Minimum touch target size (44px)
  minTouchTarget: 'min-h-[44px] min-w-[44px]',
  
  // Touch-friendly padding
  touchPadding: 'p-3 md:p-2',
  
  // Prevent text selection on interactive elements
  noSelect: 'select-none',
  
  // Enable touch manipulation
  touchManipulation: 'touch-manipulation',
  
  // Combined touch-friendly class
  touchFriendly: 'touch-manipulation select-none min-h-[44px]'
};

// Validation helpers
export const validationHelpers = {
  // Common validation patterns
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/.+/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  
  // Path validation
  absolutePath: /^([a-zA-Z]:|\/)/,
  
  // Validation functions
  isValidEmail: (email: string) => validationHelpers.email.test(email),
  isValidUrl: (url: string) => validationHelpers.url.test(url),
  isValidPath: (path: string) => validationHelpers.absolutePath.test(path),
  
  // Security checks
  isSafePath: (path: string) => {
    const restrictedPaths = ['/etc', '/sys', '/proc', '/dev', '/boot', 'C:\\Windows', 'C:\\System32'];
    return !restrictedPaths.some(restricted => 
      path.toLowerCase().startsWith(restricted.toLowerCase())
    );
  }
};
