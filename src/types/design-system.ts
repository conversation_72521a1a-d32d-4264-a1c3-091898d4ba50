/**
 * TypeScript type definitions for the mobile-first responsive design system
 */

// Base component props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Responsive size variants
export type ResponsiveSize = 'sm' | 'md' | 'lg';
export type TouchTargetSize = 'sm' | 'md' | 'lg';

// Color variants
export type ColorVariant = 'primary' | 'secondary' | 'tertiary' | 'danger' | 'success' | 'warning';

// Button component types
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: ResponsiveSize;
  fullWidth?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

// Input component types
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: string;
  size?: ResponsiveSize;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

// Settings component types
export interface SettingsCardProps extends BaseComponentProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface SettingsSectionProps extends BaseComponentProps {
  title: string;
  description?: string;
  spacing?: 'compact' | 'normal' | 'relaxed';
}

export interface SettingsRowProps extends BaseComponentProps {
  label: string;
  description?: string;
  action: React.ReactNode;
  layout?: 'horizontal' | 'vertical' | 'auto';
  spacing?: 'compact' | 'normal' | 'relaxed';
}

// Modal and popup types
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

export interface PopupProps extends BaseComponentProps {
  trigger: React.ReactNode;
  title?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  offset?: number;
}

// Directory browser types
export interface DirectoryBrowserProps extends BaseComponentProps {
  onSelect: (path: string) => void;
  onCancel: () => void;
  initialPath?: string;
  allowedExtensions?: string[];
  mode?: 'directory' | 'file' | 'both';
  multiple?: boolean;
}

export interface DirectoryItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified?: Date;
  permissions?: {
    read: boolean;
    write: boolean;
    execute: boolean;
  };
}

// Form validation types
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface FormFieldProps extends BaseComponentProps {
  name: string;
  label?: string;
  description?: string;
  required?: boolean;
  validation?: ValidationRule;
  error?: string;
}

// Responsive breakpoint types
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface ResponsiveValue<T> {
  base?: T;
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}

// Theme types
export interface ThemeColors {
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    overlay: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    muted: string;
  };
  border: {
    primary: string;
    secondary: string;
    accent: string;
  };
  interactive: {
    primary: string;
    secondary: string;
    danger: string;
    success: string;
  };
}

// Animation types
export interface AnimationConfig {
  duration?: number;
  easing?: 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  delay?: number;
}

// Touch interaction types
export interface TouchConfig {
  minTouchTarget: number;
  touchPadding: number;
  swipeThreshold: number;
  tapTimeout: number;
}

// Accessibility types
export interface A11yProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  role?: string;
  tabIndex?: number;
}

// Component state types
export type ComponentState = 'idle' | 'loading' | 'success' | 'error' | 'disabled';

export interface StatefulComponentProps extends BaseComponentProps {
  state?: ComponentState;
  onStateChange?: (state: ComponentState) => void;
}

// Layout types
export interface LayoutProps extends BaseComponentProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: ResponsiveValue<string>;
  margin?: ResponsiveValue<string>;
  gap?: ResponsiveValue<string>;
}

// Grid system types
export interface GridProps extends BaseComponentProps {
  columns?: ResponsiveValue<number>;
  gap?: ResponsiveValue<string>;
  autoFit?: boolean;
  minItemWidth?: string;
}

// Utility types
export type ClassNameValue = string | undefined | null | false;
export type ClassNameArray = ClassNameValue[];
export type ClassName = ClassNameValue | ClassNameArray;

// Component ref types
export type ButtonRef = React.RefObject<HTMLButtonElement>;
export type InputRef = React.RefObject<HTMLInputElement>;
export type DivRef = React.RefObject<HTMLDivElement>;

// Event handler types
export type ClickHandler = (event: React.MouseEvent) => void;
export type ChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;
export type KeyboardHandler = (event: React.KeyboardEvent) => void;
export type TouchHandler = (event: React.TouchEvent) => void;

// Settings context types
export interface SettingsContextValue {
  // Add settings-specific types here
  theme: 'light' | 'dark' | 'auto';
  language: string;
  accessibility: {
    reduceMotion: boolean;
    highContrast: boolean;
    largeText: boolean;
  };
}

// Media player types (for context)
export interface MediaPlayerSettings {
  defaultVolume: number;
  playbackQuality: 'low' | 'medium' | 'high' | 'lossless';
  crossfadeDuration: number;
  enableCrossfade: boolean;
  enableEqualizer: boolean;
  equalizerPreset: string;
  enableReplayGain: boolean;
  enableGaplessPlayback: boolean;
  bufferSize: number;
  enableVisualization: boolean;
}

// Directory management types
export interface DirectoryValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
  permissions?: {
    read: boolean;
    write: boolean;
    accessible: boolean;
  };
}

export interface DirectoryAddResult {
  success: boolean;
  path: string;
  error?: string;
  filesFound?: number;
  subdirectories?: number;
}
