<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Media Player - Settings Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-black text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-semibold mb-6">Settings Test</h1>
        
        <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4">Helper App URL</h3>
            <input
                type="text"
                id="helperAppUrl"
                value="http://localhost:3001"
                class="w-full p-2 bg-gray-700 rounded text-white"
            />
        </div>
        
        <div>
            <h3 class="text-xl font-semibold mb-4">Music Directories</h3>
            <div class="flex mb-4">
                <input
                    type="text"
                    id="newDir"
                    class="flex-1 p-2 bg-gray-700 rounded-l text-white"
                    placeholder="Enter a new directory path"
                />
                <button 
                    id="addBtn" 
                    class="bg-teal-500 text-white font-semibold py-2 px-4 rounded-r hover:bg-teal-600"
                >
                    Add
                </button>
            </div>
            <ul id="musicDirsList" class="space-y-2">
                <!-- Music directories will be listed here -->
            </ul>
        </div>
        
        <div class="mt-8">
            <h3 class="text-xl font-semibold mb-4">Console Output</h3>
            <div id="console" class="bg-gray-800 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
                <!-- Console output will appear here -->
            </div>
        </div>
    </div>

    <script>
        let musicDirs = [];
        let helperAppUrl = 'http://localhost:3001';
        
        // Console logging function
        function log(message) {
            const consoleDiv = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            consoleDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        // Fetch config from helper app
        async function fetchConfig() {
            try {
                log('Fetching config from helper app...');
                const response = await fetch(`${helperAppUrl}/api/config`);
                const data = await response.json();
                musicDirs = data.musicDirs || [];
                log(`Config fetched: ${JSON.stringify(data)}`);
                renderMusicDirs();
            } catch (error) {
                log(`Error fetching config: ${error.message}`);
            }
        }
        
        // Save config to helper app
        async function saveConfig(newDirs) {
            try {
                log(`Saving config: ${JSON.stringify({ musicDirs: newDirs })}`);
                const response = await fetch(`${helperAppUrl}/api/config`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ musicDirs: newDirs }),
                });
                const result = await response.json();
                log(`Config saved: ${JSON.stringify(result)}`);
                musicDirs = newDirs;
                renderMusicDirs();
            } catch (error) {
                log(`Error saving config: ${error.message}`);
            }
        }
        
        // Handle add directory
        function handleAddDir() {
            const newDirInput = document.getElementById('newDir');
            const newDir = newDirInput.value.trim();
            
            log('handleAddDir called');
            log(`newDir: "${newDir}"`);
            log(`musicDirs: ${JSON.stringify(musicDirs)}`);
            
            if (newDir && !musicDirs.includes(newDir)) {
                log(`Adding new directory: "${newDir}"`);
                const newDirs = [...musicDirs, newDir];
                saveConfig(newDirs);
                newDirInput.value = '';
            } else {
                log('Condition not met: newDir is empty or already exists.');
            }
        }
        
        // Handle remove directory
        function handleRemoveDir(dirToRemove) {
            log(`Removing directory: "${dirToRemove}"`);
            const newDirs = musicDirs.filter(dir => dir !== dirToRemove);
            saveConfig(newDirs);
        }
        
        // Render music directories list
        function renderMusicDirs() {
            const listElement = document.getElementById('musicDirsList');
            listElement.innerHTML = '';
            
            musicDirs.forEach(dir => {
                const listItem = document.createElement('li');
                listItem.className = 'flex justify-between items-center bg-gray-700 p-2 rounded';
                listItem.innerHTML = `
                    <span>${dir}</span>
                    <button onclick="handleRemoveDir('${dir}')" class="text-red-500 hover:text-red-400">
                        Remove
                    </button>
                `;
                listElement.appendChild(listItem);
            });
        }
        
        // Event listeners
        document.getElementById('addBtn').addEventListener('click', handleAddDir);
        
        document.getElementById('newDir').addEventListener('input', function(e) {
            log(`Input changed: "${e.target.value}"`);
        });
        
        document.getElementById('newDir').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleAddDir();
            }
        });
        
        document.getElementById('helperAppUrl').addEventListener('change', function(e) {
            helperAppUrl = e.target.value;
            log(`Helper app URL changed to: ${helperAppUrl}`);
        });
        
        // Initialize
        log('Initializing settings test page...');
        fetchConfig();
    </script>
</body>
</html>
