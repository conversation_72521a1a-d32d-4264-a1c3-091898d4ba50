#!/bin/bash

echo "🚀 Wayne's Media Player - Node.js Upgrade Script"
echo "================================================"

# Check current Node.js version
echo "Current Node.js version:"
node --version

echo ""
echo "📦 Installing Node Version Manager (nvm)..."

# Download and install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Source nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

echo ""
echo "🔄 Installing Node.js 18 LTS..."

# Install Node.js 18 LTS
nvm install 18
nvm use 18
nvm alias default 18

echo ""
echo "✅ Node.js upgrade complete!"
echo "New Node.js version:"
node --version

echo ""
echo "🧹 Cleaning up old dependencies..."
rm -rf node_modules package-lock.json

echo ""
echo "📦 Installing dependencies..."
npm install

echo ""
echo "🎉 Setup complete! You can now run:"
echo "   npm run dev"
echo ""
echo "📱 Your PWA will be available at http://localhost:3000"
